# Tính năng Auto-Save khi dùng lệnh /save

## <PERSON><PERSON> tả
Tính năng này tự động lưu toàn bộ dữ liệu plugin (kho khoáng sản và thống kê) của tất cả người chơi đang online khi admin sử dụng lệnh `/save` hoặc `/save-all`.

## Lợi ích
- **B<PERSON><PERSON> vệ dữ liệu**: Giúp admin lưu dữ liệu gấp khi gặp vấn đề
- **Tự động hóa**: Không cần nhớ lưu dữ liệu plugin riêng biệt
- **Hiệu quả**: Chạy bất đồng bộ, không ảnh hưởng đến hiệu suất server
- **Tương thích**: Hoạt động với Minecraft 1.12.2 - 1.21.4

## Cách hoạt động

### 1. <PERSON><PERSON> nà<PERSON> được kích hoạt
- Admin gõ lệnh `/save` từ console hoặc in-game
- Admin gõ lệnh `/save-all` từ console hoặc in-game
- Admin có quyền `storage.admin` hoặc là OP

### 2. Qu<PERSON> trình lưu dữ liệu
1. Listener bắt sự kiện lệnh `/save` hoặc `/save-all`
2. Kiểm tra cấu hình `database.save_on_server_save` (mặc định: true)
3. Chạy bất đồng bộ để không ảnh hưởng lệnh gốc
4. Lưu dữ liệu kho khoáng sản cho tất cả người chơi online
5. Lưu dữ liệu thống kê cho tất cả người chơi online
6. Ghi log tiến trình và kết quả
7. Thông báo cho admin (nếu là người chơi thực thi)

## Cấu hình

### config.yml
```yaml
database:
  # Bật/tắt tự động lưu dữ liệu plugin khi admin dùng lệnh /save hoặc /save-all
  save_on_server_save: true
```

### message.yml
```yaml
admin:
  auto_save:
    success: "&a[Storage] Đã tự động lưu dữ liệu cho #count# người chơi!"
    start: "&e[Auto-Save] Bắt đầu lưu dữ liệu plugin..."
    complete: "&a[Auto-Save] Hoàn thành! Đã lưu dữ liệu cho #saved#/#total# người chơi"
```

## Thông báo

### Console Log
```
[INFO] [Auto-Save] Bắt đầu lưu dữ liệu plugin... (do Console thực thi lệnh save)
[INFO] [Auto-Save] Đã lưu: 10/50 người chơi
[INFO] [Auto-Save] Đã lưu: 20/50 người chơi
...
[INFO] [Auto-Save] Hoàn thành! Đã lưu dữ liệu cho 50/50 người chơi
```

### Thông báo cho Admin (in-game)
```
[Storage] Đã tự động lưu dữ liệu cho 50 người chơi!
```

## Tính năng kỹ thuật

### Tương thích đa phiên bản
- Sử dụng Bukkit API chuẩn
- Tương thích với Minecraft 1.12.2 - 1.21.4
- Không cần NMS hoặc reflection

### Hiệu suất
- **Bất đồng bộ**: Chạy trên thread riêng, không block main thread
- **Không ảnh hưởng lệnh gốc**: Lệnh `/save` vẫn hoạt động bình thường
- **Log thông minh**: Chỉ log tiến trình mỗi 10 người chơi để tránh spam
- **Xử lý lỗi**: Có try-catch để tránh crash server

### Bảo mật
- Kiểm tra quyền admin (`storage.admin` hoặc OP)
- Kiểm tra cấu hình trước khi thực thi
- Xử lý an toàn các trường hợp lỗi

## Cách sử dụng

### 1. Bật/tắt tính năng
Chỉnh sửa `config.yml`:
```yaml
database:
  save_on_server_save: true  # Bật
  save_on_server_save: false # Tắt
```

### 2. Sử dụng lệnh
```bash
# Từ console
save
save-all

# Từ in-game (cần quyền admin)
/save
/save-all
```

### 3. Kiểm tra log
Xem console để theo dõi quá trình lưu dữ liệu:
```
[INFO] [Auto-Save] Bắt đầu lưu dữ liệu plugin...
[INFO] [Auto-Save] Hoàn thành! Đã lưu dữ liệu cho X/Y người chơi
```

## Xử lý sự cố

### Tính năng không hoạt động
1. Kiểm tra cấu hình `database.save_on_server_save` trong `config.yml`
2. Đảm bảo admin có quyền `storage.admin` hoặc là OP
3. Kiểm tra console log có thông báo lỗi không

### Lỗi khi lưu dữ liệu
- Plugin sẽ ghi log chi tiết lỗi trong console
- Dữ liệu của người chơi khác vẫn được lưu bình thường
- Không ảnh hưởng đến hoạt động của server

### Hiệu suất chậm
- Tính năng chạy bất đồng bộ nên không ảnh hưởng TPS
- Nếu có nhiều người chơi, quá trình lưu có thể mất vài giây
- Có thể tắt tính năng nếu không cần thiết

## Lưu ý quan trọng

1. **Không thay thế backup thường xuyên**: Đây chỉ là tính năng bổ sung
2. **Chỉ lưu người chơi online**: Dữ liệu người chơi offline không được lưu
3. **Cần quyền admin**: Chỉ admin mới có thể kích hoạt tính năng này
4. **Có thể tắt**: Có thể tắt tính năng thông qua config nếu không cần

## Kết luận

Tính năng Auto-Save khi dùng lệnh `/save` giúp admin bảo vệ dữ liệu plugin một cách tự động và hiệu quả. Tính năng được thiết kế đơn giản nhưng mạnh mẽ, tương thích đa phiên bản và không ảnh hưởng đến hiệu suất server.
