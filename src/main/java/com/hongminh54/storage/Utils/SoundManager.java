package com.hongminh54.storage.Utils;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;

import com.hongminh54.storage.NMS.NMSAssistant;

/**
 * Lớp quản lý âm thanh cho Minecraft 1.12.2 - 1.21.x
 */
public class SoundManager {
    
    private static final boolean IS_PRE_113 = new NMSAssistant().isVersionLessThan(13);
    
    // <PERSON><PERSON><PERSON> phương thức chuyển đổi đã được chuyển sang SoundCompatibility
    
    // Các phương thức xử lý Sound enum đã được chuyển sang SoundCompatibility
    
    /**
     * Phát âm thanh cho người chơi
     *
     * @param player Người chơi nghe âm thanh
     * @param soundName Tên âm thanh
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    public static void playSound(Player player, String soundName, float volume, float pitch) {
        // Sử dụng SoundCompatibility với String-only approach
        SoundCompatibility.playSound(player, soundName, volume, pitch);
    }
    
    /**
     * Phát âm thanh cho người chơi
     * 
     * @param player Người chơi nghe âm thanh
     * @param sound Enum Sound
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    public static void playSound(Player player, Sound sound, float volume, float pitch) {
        if (player == null || sound == null) {
            return;
        }
        
        try {
            player.playSound(player.getLocation(), sound, volume, pitch);
        } catch (Exception e) {
            // Ghi log lỗi
            System.out.println("Không thể phát âm thanh: " + sound.name() + " - " + e.getMessage());
        }
    }
    
    /**
     * Phát âm thanh tại vị trí cụ thể
     *
     * @param location Vị trí phát âm thanh
     * @param soundName Tên âm thanh
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    public static void playSound(Location location, String soundName, float volume, float pitch) {
        if (location == null || location.getWorld() == null || soundName == null || soundName.isEmpty()) {
            return;
        }

        try {
            // Sử dụng SoundCompatibilityV2 để phát âm thanh an toàn
            try {
                location.getWorld().playSound(location, soundName, volume, pitch);
            } catch (Exception e1) {
                // Thử với tên âm thanh đã chuyển đổi
                String convertedName = convertSoundNameForLocation(soundName);
                try {
                    location.getWorld().playSound(location, convertedName, volume, pitch);
                } catch (Exception e2) {
                    // Bỏ qua lỗi nhưng ghi log
                    System.out.println("Không thể phát âm thanh tại vị trí: " + soundName);
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
            System.out.println("Lỗi khi phát âm thanh: " + e.getMessage());
        }
    }
    
    /**
     * Phát âm thanh từ chuỗi cấu hình
     * Ví dụ: "ENTITY_VILLAGER_NO:1.0:1.0" hoặc "NOTE_PLING:0.5:1.2"
     *
     * @param player Người chơi
     * @param soundConfig Chuỗi cấu hình âm thanh dạng "TÊN_ÂM_THANH:VOLUME:PITCH"
     */
    public static void playSoundFromConfig(Player player, String soundConfig) {
        // Sử dụng SoundCompatibility với String-only approach
        SoundCompatibility.playSoundFromConfig(player, soundConfig);
    }
    /**
     * Chuyển đổi tên âm thanh cho location playSound
     * @param soundName Tên âm thanh gốc
     * @return Tên âm thanh đã chuyển đổi
     */
    private static String convertSoundNameForLocation(String soundName) {
        // Sử dụng logic tương tự SoundCompatibilityV2
        if (IS_PRE_113) {
            // Chuyển từ tên mới sang tên cũ
            switch (soundName) {
                case "BLOCK_NOTE_BLOCK_PLING": return "NOTE_PLING";
                case "BLOCK_CHEST_OPEN": return "CHEST_OPEN";
                case "UI_BUTTON_CLICK": return "CLICK";
                case "ENTITY_VILLAGER_NO": return "VILLAGER_NO";
                default: return soundName;
            }
        } else {
            // Chuyển từ tên cũ sang tên mới
            switch (soundName) {
                case "NOTE_PLING": return "BLOCK_NOTE_BLOCK_PLING";
                case "CHEST_OPEN": return "BLOCK_CHEST_OPEN";
                case "CLICK": return "UI_BUTTON_CLICK";
                case "VILLAGER_NO": return "ENTITY_VILLAGER_NO";
                default: return soundName;
            }
        }
    }
}