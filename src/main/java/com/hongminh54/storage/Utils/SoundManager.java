package com.hongminh54.storage.Utils;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;

import com.hongminh54.storage.NMS.NMSAssistant;

/**
 * Lớp quản lý âm thanh cho Minecraft 1.12.2 - 1.21.x
 */
public class SoundManager {
    
    private static final boolean IS_PRE_113 = new NMSAssistant().isVersionLessThan(13);
    
    // <PERSON><PERSON><PERSON> phương thức chuyển đổi đã được chuyển sang SoundCompatibility
    
    // Các phương thức xử lý Sound enum đã được chuyển sang SoundCompatibility
    
    /**
     * Phát âm thanh cho người chơi
     *
     * @param player Người chơi nghe âm thanh
     * @param soundName Tên âm thanh
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    public static void playSound(Player player, String soundName, float volume, float pitch) {
        // S<PERSON> dụng SoundCompatibility để xử lý an toàn hơn
        SoundCompatibility.playSound(player, soundName, volume, pitch);
    }
    
    /**
     * Phát âm thanh cho người chơi
     * 
     * @param player Người chơi nghe âm thanh
     * @param sound Enum Sound
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    public static void playSound(Player player, Sound sound, float volume, float pitch) {
        if (player == null || sound == null) {
            return;
        }
        
        try {
            player.playSound(player.getLocation(), sound, volume, pitch);
        } catch (Exception e) {
            // Ghi log lỗi
            System.out.println("Không thể phát âm thanh: " + sound.name() + " - " + e.getMessage());
        }
    }
    
    /**
     * Phát âm thanh tại vị trí cụ thể
     *
     * @param location Vị trí phát âm thanh
     * @param soundName Tên âm thanh
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    public static void playSound(Location location, String soundName, float volume, float pitch) {
        if (location == null || location.getWorld() == null || soundName == null || soundName.isEmpty()) {
            return;
        }

        try {
            // Sử dụng SoundCompatibility để lấy âm thanh tương thích
            Sound sound = SoundCompatibility.getCompatibleSound(soundName);

            if (sound != null) {
                // Phát âm thanh thông qua enum
                location.getWorld().playSound(location, sound, volume, pitch);
            } else {
                try {
                    // Thử với tên chuỗi
                    location.getWorld().playSound(location, soundName, volume, pitch);
                } catch (Exception ex) {
                    // Bỏ qua lỗi nhưng ghi log
                    System.out.println("Không thể phát âm thanh tại vị trí: " + soundName);
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
            System.out.println("Lỗi khi phát âm thanh: " + e.getMessage());
        }
    }
    
    /**
     * Phát âm thanh từ chuỗi cấu hình
     * Ví dụ: "ENTITY_VILLAGER_NO:1.0:1.0" hoặc "NOTE_PLING:0.5:1.2"
     *
     * @param player Người chơi
     * @param soundConfig Chuỗi cấu hình âm thanh dạng "TÊN_ÂM_THANH:VOLUME:PITCH"
     */
    public static void playSoundFromConfig(Player player, String soundConfig) {
        // Sử dụng SoundCompatibility để xử lý an toàn hơn
        SoundCompatibility.playSoundFromConfig(player, soundConfig);
    }
    // Phương thức fallback đã được chuyển sang SoundCompatibility
}