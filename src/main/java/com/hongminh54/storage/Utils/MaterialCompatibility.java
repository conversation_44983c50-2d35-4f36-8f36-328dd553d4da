package com.hongminh54.storage.Utils;

import java.util.Optional;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffectType;

import com.cryptomorin.xseries.XEnchantment;
import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

/**
 * Lớp này hỗ trợ tương thích các vật liệu (Material) giữa các phiên bản Minecraft 1.12.2 - 1.21.x
 * 
 * Minecraft 1.13+ đã thay đổi nhiều tên Material, lớp này giúp chuyển đổi giữa các phiên bản.
 */
public class MaterialCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean PRE_1_13 = nmsAssistant.isVersionLessThan(13);
    
    /**
     * Lấy vật liệu tương ứng dựa trên phiên bản của máy chủ
     * Giúp chuyển đổi giữa tên material cũ (1.12.2) và mới (1.13+)
     *
     * @param modernMaterial Tên vật liệu ở phiên bản 1.13+
     * @param legacyMaterial Tên vật liệu ở phiên bản 1.12.2
     * @return Material tương ứng với phiên bản hiện tại của máy chủ
     */
    public static Material getMaterial(String modernMaterial, String legacyMaterial) {
        try {
            if (PRE_1_13) {
                return Material.valueOf(legacyMaterial);
            } else {
                return Material.valueOf(modernMaterial);
            }
        } catch (IllegalArgumentException e) {
            Storage.getStorage().getLogger().warning("Không tìm thấy vật liệu: " + (PRE_1_13 ? legacyMaterial : modernMaterial) + ", sử dụng fallback đơn giản");
            // Sử dụng fallback đơn giản để tránh infinite recursion
            return Material.PAPER; // Fallback an toàn
        }
    }
    
    /**
     * Lấy vật liệu glass pane, phù hợp với phiên bản hiện tại
     * 
     * @param color Màu sắc (WHITE, ORANGE, MAGENTA, LIGHT_BLUE, YELLOW, LIME, PINK, GRAY, LIGHT_GRAY, CYAN, PURPLE, BLUE, BROWN, GREEN, RED, BLACK)
     * @return Material tương ứng
     */
    public static Material getStainedGlassPane(String color) {
        try {
            if (PRE_1_13) {
                return Material.valueOf("STAINED_GLASS_PANE");
            } else {
                return Material.valueOf(color + "_STAINED_GLASS_PANE");
            }
        } catch (IllegalArgumentException e) {
            // Fallback an toàn
            if (PRE_1_13) {
                return Material.valueOf("STAINED_GLASS_PANE");
            } else {
                return Material.valueOf("WHITE_STAINED_GLASS_PANE");
            }
        }
    }
    
    /**
     * Lấy vật liệu glass, phù hợp với phiên bản hiện tại
     * 
     * @param color Màu sắc (WHITE, ORANGE, MAGENTA, LIGHT_BLUE, YELLOW, LIME, PINK, GRAY, LIGHT_GRAY, CYAN, PURPLE, BLUE, BROWN, GREEN, RED, BLACK)
     * @return Material tương ứng
     */
    public static Material getStainedGlass(String color) {
        if (PRE_1_13) {
            return Material.valueOf("STAINED_GLASS");
        } else {
            return Material.valueOf(color + "_STAINED_GLASS");
        }
    }
    
    /**
     * Lấy vật liệu theo tên hiện đại, nếu không có sẽ sử dụng fallback
     * 
     * @param modernMaterial Tên vật liệu ở phiên bản mới
     * @param fallback Vật liệu mặc định nếu không tìm thấy
     * @return Material tương ứng hoặc fallback
     */
    public static Material getMaterialWithFallback(String modernMaterial, Material fallback) {
        try {
            return Material.valueOf(modernMaterial);
        } catch (IllegalArgumentException e) {
            return fallback;
        }
    }
    
    /**
     * Chuyển đổi giá trị byte dữ liệu sang tên vật liệu cho phiên bản 1.13+
     * Chỉ có tác dụng với 1.12.2
     * 
     * @param material Material gốc
     * @param data Giá trị data
     * @return Tên vật liệu hoặc tên vật liệu với data
     */
    public static String getLegacyMaterialName(String material, byte data) {
        if (PRE_1_13) {
            if (data == 0) {
                return material;
            } else {
                return material + ";" + data;
            }
        }
        
        // Chuyển đổi từ legacy sang modern Material
        if (material.equalsIgnoreCase("STAINED_CLAY")) {
            // Xử lý STAINED_CLAY
            String[] colors = {"WHITE", "ORANGE", "MAGENTA", "LIGHT_BLUE", 
                              "YELLOW", "LIME", "PINK", "GRAY", 
                              "LIGHT_GRAY", "CYAN", "PURPLE", "BLUE", 
                              "BROWN", "GREEN", "RED", "BLACK"};
            if (data >= 0 && data < colors.length) {
                return colors[data] + "_TERRACOTTA";
            }
            return "TERRACOTTA";
        } else if (material.equalsIgnoreCase("WOOL")) {
            // Xử lý WOOL
            String[] colors = {"WHITE", "ORANGE", "MAGENTA", "LIGHT_BLUE", 
                              "YELLOW", "LIME", "PINK", "GRAY", 
                              "LIGHT_GRAY", "CYAN", "PURPLE", "BLUE", 
                              "BROWN", "GREEN", "RED", "BLACK"};
            if (data >= 0 && data < colors.length) {
                return colors[data] + "_WOOL";
            }
            return "WHITE_WOOL";
        } else if (material.equalsIgnoreCase("WOOD") || material.equalsIgnoreCase("LOG") || material.equalsIgnoreCase("LOG_2")) {
            // Xử lý gỗ
            if (material.equalsIgnoreCase("LOG")) {
                if (data == 0) return "OAK_LOG";
                if (data == 1) return "SPRUCE_LOG";
                if (data == 2) return "BIRCH_LOG";
                if (data == 3) return "JUNGLE_LOG";
            }
            if (material.equalsIgnoreCase("LOG_2")) {
                if (data == 0) return "ACACIA_LOG";
                if (data == 1) return "DARK_OAK_LOG";
            }
            if (material.equalsIgnoreCase("WOOD")) {
                return "OAK_WOOD";
            }
            return "OAK_LOG";
        } 
        
        return material;
    }
    
    /**
     * Kiểm tra xem một vật liệu có phải là loại kính màu không
     * 
     * @param material Vật liệu cần kiểm tra
     * @return true nếu là kính màu
     */
    public static boolean isStainedGlassPane(Material material) {
        String name = material.name();
        if (PRE_1_13) {
            return name.equals("STAINED_GLASS_PANE");
        } else {
            return name.endsWith("_STAINED_GLASS_PANE");
        }
    }
    
    /**
     * Kiểm tra xem server hiện tại có đang chạy phiên bản trước 1.13 không
     *
     * @return true nếu server đang chạy 1.12.2
     */
    public static boolean isPre113() {
        return PRE_1_13;
    }

    /**
     * Lấy âm thanh tương thích với phiên bản hiện tại
     * Hỗ trợ fallback cho các âm thanh không tồn tại trong phiên bản cũ
     *
     * @param modernSound Tên âm thanh phiên bản mới (1.13+)
     * @param legacySound Tên âm thanh phiên bản cũ (1.12.2)
     * @param fallbackSound Âm thanh fallback an toàn
     * @return Tên âm thanh tương thích
     */
    public static String getCompatibleSound(String modernSound, String legacySound, String fallbackSound) {
        if (PRE_1_13) {
            // Sử dụng âm thanh phiên bản cũ hoặc fallback
            return legacySound != null ? legacySound : fallbackSound;
        } else {
            // Sử dụng âm thanh phiên bản mới
            return modernSound;
        }
    }

    /**
     * Lấy âm thanh tương thích với fallback mặc định
     *
     * @param modernSound Tên âm thanh phiên bản mới
     * @param legacySound Tên âm thanh phiên bản cũ
     * @return Tên âm thanh tương thích
     */
    public static String getCompatibleSound(String modernSound, String legacySound) {
        return getCompatibleSound(modernSound, legacySound, "ENTITY_EXPERIENCE_ORB_PICKUP");
    }

    /**
     * Tạo ItemStack từ material string với xử lý tương thích đa phiên bản
     * @param materialConfig Cấu hình material
     * @return ItemStack được tạo
     */
    public static ItemStack createCompatibleItemStack(String materialConfig) {
        try {
            // Xử lý material với mapping tương thích
            String compatibleMaterial = getCompatibleMaterial(materialConfig);

            if (PRE_1_13) {
                // Phiên bản 1.12.2 - xử lý material cũ
                if (compatibleMaterial.contains(":")) {
                    String[] parts = compatibleMaterial.split(":");
                    Material mat = Material.valueOf(parts[0]);
                    byte data = Byte.parseByte(parts[1]);
                    return new ItemStack(mat, 1, data);
                } else {
                    return new ItemStack(Material.valueOf(compatibleMaterial));
                }
            } else {
                // Phiên bản 1.13+ - sử dụng material mới
                return new ItemStack(Material.valueOf(materialConfig));
            }
        } catch (IllegalArgumentException e) {
            // Thử với XMaterial nếu Material.valueOf thất bại
            try {
                Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialConfig);
                if (xMaterial.isPresent()) {
                    ItemStack xItem = xMaterial.get().parseItem();
                    if (xItem != null) {
                        return xItem;
                    }
                }
            } catch (Exception ex) {
                // Bỏ qua lỗi XMaterial
            }

            // Fallback cuối cùng với material phù hợp
            return getSmartFallbackItem(materialConfig);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi tạo ItemStack từ material: " + materialConfig + " - " + e.getMessage());
            return getSmartFallbackItem(materialConfig);
        }
    }

    /**
     * Lấy material tương thích giữa các phiên bản với mapping đầy đủ
     * @param materialName Tên material gốc
     * @return Tên material tương thích
     */
    public static String getCompatibleMaterial(String materialName) {
        if (PRE_1_13) {
            // Mapping đầy đủ cho phiên bản 1.12.2
            return getLegacyMaterialMapping(materialName.toUpperCase());
        }
        return materialName;
    }

    /**
     * Lấy Material enum một cách an toàn với xử lý lỗi
     * @param materialName Tên material
     * @return Material enum hoặc null nếu không tìm thấy
     */
    public static Material getMaterialSafely(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            return null;
        }

        try {
            // Thử với tên gốc trước
            return Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            try {
                // Thử với mapping tương thích
                String compatibleName = getCompatibleMaterial(materialName);
                if (compatibleName.contains(":")) {
                    // Xử lý format "MATERIAL:DATA" cho 1.12.2
                    String[] parts = compatibleName.split(":");
                    return Material.valueOf(parts[0].toUpperCase());
                } else {
                    return Material.valueOf(compatibleName.toUpperCase());
                }
            } catch (IllegalArgumentException ex) {
                // Thử với XMaterial
                try {
                    Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialName);
                    if (xMaterial.isPresent()) {
                        return xMaterial.get().parseMaterial();
                    }
                } catch (Exception xEx) {
                    // Bỏ qua lỗi XMaterial
                }
                return null;
            }
        }
    }

    /**
     * Mapping đầy đủ material từ 1.13+ về 1.12.2
     * @param modernMaterial Material của phiên bản 1.13+
     * @return Material tương ứng cho 1.12.2
     */
    public static String getLegacyMaterialMapping(String modernMaterial) {
        switch (modernMaterial) {
            // Đầu người chơi và mob
            case "PLAYER_HEAD": return "SKULL_ITEM:3";
            case "ZOMBIE_HEAD": return "SKULL_ITEM:2";
            case "CREEPER_HEAD": return "SKULL_ITEM:4";
            case "SKELETON_SKULL": return "SKULL_ITEM:0";
            case "WITHER_SKELETON_SKULL": return "SKULL_ITEM:1";
            case "DRAGON_HEAD": return "SKULL_ITEM:5";

            // Biển gỗ
            case "OAK_SIGN": return "SIGN";
            case "SPRUCE_SIGN": return "SIGN";
            case "BIRCH_SIGN": return "SIGN";
            case "JUNGLE_SIGN": return "SIGN";
            case "ACACIA_SIGN": return "SIGN";
            case "DARK_OAK_SIGN": return "SIGN";

            // Hoa và cây
            case "SUNFLOWER": return "DOUBLE_PLANT:0";
            case "LILAC": return "DOUBLE_PLANT:1";
            case "TALL_GRASS": return "DOUBLE_PLANT:2";
            case "LARGE_FERN": return "DOUBLE_PLANT:3";
            case "ROSE_BUSH": return "DOUBLE_PLANT:4";
            case "PEONY": return "DOUBLE_PLANT:5";

            // Kính màu
            case "WHITE_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:0";
            case "ORANGE_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:1";
            case "MAGENTA_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:2";
            case "LIGHT_BLUE_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:3";
            case "YELLOW_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:4";
            case "LIME_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:5";
            case "PINK_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:6";
            case "GRAY_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:7";
            case "LIGHT_GRAY_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:8";
            case "CYAN_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:9";
            case "PURPLE_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:10";
            case "BLUE_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:11";
            case "BROWN_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:12";
            case "GREEN_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:13";
            case "RED_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:14";
            case "BLACK_STAINED_GLASS_PANE": return "STAINED_GLASS_PANE:15";

            default:
                return modernMaterial;
        }
    }

    /**
     * Lấy item fallback thông minh thay vì STONE
     * @param originalMaterial Material gốc
     * @return ItemStack fallback phù hợp
     */
    public static ItemStack getSmartFallbackItem(String originalMaterial) {
        if (originalMaterial == null || originalMaterial.isEmpty()) {
            return new ItemStack(Material.PAPER);
        }

        String upperMaterial = originalMaterial.toUpperCase();

        // Xử lý format "MATERIAL;DATA" trước tiên
        if (upperMaterial.contains(";")) {
            try {
                String[] parts = upperMaterial.split(";");
                String materialName = parts[0];
                byte data = parts.length > 1 ? Byte.parseByte(parts[1]) : 0;

                // Thử tạo ItemStack với material và data
                Material mat = Material.valueOf(materialName);
                if (PRE_1_13) {
                    return new ItemStack(mat, 1, data);
                } else {
                    // Phiên bản 1.13+ bỏ qua data value
                    return new ItemStack(mat, 1);
                }
            } catch (Exception e) {
                // Nếu không thành công, thử với tên material không có data
                try {
                    String materialName = upperMaterial.split(";")[0];
                    Material mat = Material.valueOf(materialName);
                    return new ItemStack(mat, 1);
                } catch (Exception ex) {
                    // Tiếp tục với logic fallback khác
                }
            }
        }

        // Thử tạo với mapping trước
        try {
            String legacyMaterial = getLegacyMaterialMapping(upperMaterial);
            if (!legacyMaterial.equals(upperMaterial)) {
                // Có mapping, thử tạo với legacy material
                if (legacyMaterial.contains(":")) {
                    String[] parts = legacyMaterial.split(":");
                    Material mat = Material.valueOf(parts[0]);
                    byte data = Byte.parseByte(parts[1]);
                    return new ItemStack(mat, 1, data);
                } else {
                    return new ItemStack(Material.valueOf(legacyMaterial));
                }
            }
        } catch (Exception e) {
            // Tiếp tục với logic fallback khác
        }

        // Thử với Material.valueOf trực tiếp để tránh infinite recursion
        try {
            String materialName = upperMaterial.contains(";") ? upperMaterial.split(";")[0] : upperMaterial;
            Material compatibleMaterial = Material.valueOf(materialName);
            if (compatibleMaterial != null) {
                return new ItemStack(compatibleMaterial);
            }
        } catch (Exception e) {
            // Tiếp tục với logic fallback khác
        }

        // Fallback dựa trên loại material
        if (isMaterialType(upperMaterial, "HEAD", "SKULL")) {
            return createFallbackHead();
        } else if (isMaterialType(upperMaterial, "SIGN")) {
            return createFallbackSign();
        } else if (isMaterialType(upperMaterial, "GLASS_PANE", "STAINED_GLASS_PANE")) {
            return createFallbackGlassPane();
        } else if (isMaterialType(upperMaterial, "WOOL")) {
            return createFallbackWool();
        } else if (isMaterialType(upperMaterial, "TERRACOTTA", "STAINED_CLAY")) {
            return createFallbackTerracotta();
        } else if (isMaterialType(upperMaterial, "PLANKS", "WOOD")) {
            return createFallbackWood();
        } else if (isMaterialType(upperMaterial, "FLOWER", "PLANT", "SUNFLOWER", "LILAC", "ROSE")) {
            return createFallbackFlower();
        } else {
            // Fallback cuối cùng - chỉ log warning cho material không phổ biến
            String materialName = upperMaterial.contains(";") ? upperMaterial.split(";")[0] : upperMaterial;
            if (!isCommonMaterial(materialName)) {
                Storage.getStorage().getLogger().warning("Không tìm thấy fallback cho material: " + originalMaterial + ", sử dụng PAPER");
            }
            return new ItemStack(Material.PAPER);
        }
    }

    /**
     * Kiểm tra material có thuộc loại nào đó không
     */
    public static boolean isMaterialType(String material, String... types) {
        for (String type : types) {
            if (material.contains(type)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Kiểm tra xem material có phải là material phổ biến không
     * @param materialName Tên material
     * @return true nếu là material phổ biến
     */
    public static boolean isCommonMaterial(String materialName) {
        // Danh sách các material phổ biến trong plugin tài nguyên (từ config.yml)
        String[] commonMaterials = {
            // Các material chính từ config.yml
            "COBBLESTONE", "COAL", "COAL_BLOCK", "IRON_INGOT", "IRON_BLOCK",
            "GOLD_INGOT", "GOLD_BLOCK", "REDSTONE", "REDSTONE_BLOCK",
            "LAPIS_LAZULI", "LAPIS_BLOCK", "DIAMOND", "DIAMOND_BLOCK",
            "EMERALD", "EMERALD_BLOCK",
            // Các ore blocks
            "STONE", "COAL_ORE", "IRON_ORE", "GOLD_ORE", "REDSTONE_ORE",
            "LAPIS_ORE", "DIAMOND_ORE", "EMERALD_ORE",
            // Các material phổ biến khác
            "DIRT", "SAND", "GRAVEL", "NETHERRACK", "OBSIDIAN", "BEDROCK",
            "GLOWSTONE", "NETHER_QUARTZ_ORE", "QUARTZ"
        };

        for (String common : commonMaterials) {
            if (materialName.equals(common)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Tạo fallback cho đầu
     */
    public static ItemStack createFallbackHead() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (byte) 3);
        } else {
            try {
                return new ItemStack(Material.valueOf("PLAYER_HEAD"));
            } catch (IllegalArgumentException e) {
                return new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (byte) 3);
            }
        }
    }

    /**
     * Tạo fallback cho biển
     */
    public static ItemStack createFallbackSign() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("SIGN"));
        } else {
            return new ItemStack(Material.valueOf("OAK_SIGN"));
        }
    }

    /**
     * Tạo fallback cho kính
     */
    public static ItemStack createFallbackGlassPane() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), 1, (byte) 7); // Gray
        } else {
            return new ItemStack(Material.valueOf("GRAY_STAINED_GLASS_PANE"));
        }
    }

    /**
     * Tạo fallback cho len
     */
    public static ItemStack createFallbackWool() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("WOOL"), 1, (byte) 0); // White
        } else {
            return new ItemStack(Material.valueOf("WHITE_WOOL"));
        }
    }

    /**
     * Tạo fallback cho đất sét nung
     */
    public static ItemStack createFallbackTerracotta() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("STAINED_CLAY"), 1, (byte) 0); // White
        } else {
            return new ItemStack(Material.valueOf("WHITE_TERRACOTTA"));
        }
    }

    /**
     * Tạo fallback cho gỗ
     */
    public static ItemStack createFallbackWood() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("WOOD"), 1, (byte) 0); // Oak
        } else {
            return new ItemStack(Material.valueOf("OAK_PLANKS"));
        }
    }

    /**
     * Tạo fallback cho hoa
     */
    public static ItemStack createFallbackFlower() {
        if (PRE_1_13) {
            return new ItemStack(Material.valueOf("DOUBLE_PLANT"), 1, (byte) 0); // Sunflower
        } else {
            return new ItemStack(Material.valueOf("SUNFLOWER"));
        }
    }

    /**
     * Tạo ItemStack với custom model data tương thích
     * @param materialConfig Cấu hình material
     * @param customModelData Custom model data (chỉ áp dụng cho 1.14+)
     * @return ItemStack với custom model data
     */
    public static ItemStack createCompatibleItemStackWithCustomModel(String materialConfig, int customModelData) {
        ItemStack item = createCompatibleItemStack(materialConfig);

        // Áp dụng custom model data cho phiên bản 1.14+
        if (!PRE_1_13 && customModelData > 0) {
            try {
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    // Sử dụng reflection để tránh lỗi trong phiên bản cũ
                    meta.getClass().getMethod("setCustomModelData", int.class).invoke(meta, customModelData);
                    item.setItemMeta(meta);
                }
            } catch (Exception e) {
                // Phiên bản không hỗ trợ CustomModelData, bỏ qua
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Phiên bản không hỗ trợ CustomModelData: " + e.getMessage());
                }
            }
        }

        return item;
    }

    /**
     * Kiểm tra xem material có hợp lệ không
     * @param materialName Tên material
     * @return true nếu material hợp lệ
     */
    public static boolean isValidMaterial(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            return false;
        }

        try {
            // Thử với material trực tiếp
            Material.valueOf(materialName.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            // Thử với mapping
            String legacyMaterial = getLegacyMaterialMapping(materialName.toUpperCase());
            if (!legacyMaterial.equals(materialName.toUpperCase())) {
                try {
                    if (legacyMaterial.contains(":")) {
                        String[] parts = legacyMaterial.split(":");
                        Material.valueOf(parts[0]);
                        return true;
                    } else {
                        Material.valueOf(legacyMaterial);
                        return true;
                    }
                } catch (IllegalArgumentException ex) {
                    return false;
                }
            }

            // Thử với XMaterial
            try {
                Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialName);
                return xMaterial.isPresent();
            } catch (Exception ex) {
                return false;
            }
        }
    }

    /**
     * Lấy danh sách tất cả material tương thích
     * @return Danh sách material names
     */
    public static java.util.List<String> getAllCompatibleMaterials() {
        java.util.List<String> materials = new java.util.ArrayList<>();

        // Thêm tất cả material hiện tại
        for (Material material : Material.values()) {
            materials.add(material.name());
        }

        // Thêm các material mapping
        if (PRE_1_13) {
            materials.add("SKULL_ITEM:0");
            materials.add("SKULL_ITEM:1");
            materials.add("SKULL_ITEM:2");
            materials.add("SKULL_ITEM:3");
            materials.add("SKULL_ITEM:4");
            materials.add("SKULL_ITEM:5");
            materials.add("STAINED_GLASS_PANE:0");
            materials.add("STAINED_GLASS_PANE:1");
            materials.add("STAINED_GLASS_PANE:2");
            materials.add("STAINED_GLASS_PANE:3");
            materials.add("STAINED_GLASS_PANE:4");
            materials.add("STAINED_GLASS_PANE:5");
            materials.add("STAINED_GLASS_PANE:6");
            materials.add("STAINED_GLASS_PANE:7");
            materials.add("STAINED_GLASS_PANE:8");
            materials.add("STAINED_GLASS_PANE:9");
            materials.add("STAINED_GLASS_PANE:10");
            materials.add("STAINED_GLASS_PANE:11");
            materials.add("STAINED_GLASS_PANE:12");
            materials.add("STAINED_GLASS_PANE:13");
            materials.add("STAINED_GLASS_PANE:14");
            materials.add("STAINED_GLASS_PANE:15");
        }

        return materials;
    }

    /**
     * Lấy enchantment tương thích đa phiên bản
     * @param modernName Tên enchantment phiên bản mới (1.20.5+)
     * @param legacyName Tên enchantment phiên bản cũ (trước 1.20.5)
     * @return Enchantment tương thích
     */
    public static Enchantment getCompatibleEnchantment(String modernName, String legacyName) {
        try {
            // Thử với XEnchantment trước
            Optional<XEnchantment> xEnchant = XEnchantment.matchXEnchantment(modernName);
            if (xEnchant.isPresent() && xEnchant.get().getEnchant() != null) {
                return xEnchant.get().getEnchant();
            }

            // Thử với legacy name
            xEnchant = XEnchantment.matchXEnchantment(legacyName);
            if (xEnchant.isPresent() && xEnchant.get().getEnchant() != null) {
                return xEnchant.get().getEnchant();
            }

            // Fallback cuối cùng
            return Enchantment.UNBREAKING; // Safe fallback
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể tìm enchantment: " + modernName + "/" + legacyName);
            return Enchantment.UNBREAKING;
        }
    }

    /**
     * Lấy PotionEffectType tương thích đa phiên bản
     * @param modernName Tên effect phiên bản mới (1.20.5+)
     * @param legacyName Tên effect phiên bản cũ (trước 1.20.5)
     * @return PotionEffectType tương thích
     */
    public static PotionEffectType getCompatiblePotionEffect(String modernName, String legacyName) {
        try {
            // Thử với tên mới trước
            PotionEffectType effect = PotionEffectType.getByName(modernName);
            if (effect != null) {
                return effect;
            }

            // Thử với tên cũ
            effect = PotionEffectType.getByName(legacyName);
            if (effect != null) {
                return effect;
            }

            // Fallback cuối cùng
            return PotionEffectType.SPEED; // Safe fallback
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể tìm potion effect: " + modernName + "/" + legacyName);
            return PotionEffectType.SPEED;
        }
    }

    /**
     * Lấy ItemFlag tương thích đa phiên bản
     * @param modernName Tên flag phiên bản mới (1.20.5+)
     * @param legacyName Tên flag phiên bản cũ (trước 1.20.5)
     * @return ItemFlag tương thích
     */
    public static ItemFlag getCompatibleItemFlag(String modernName, String legacyName) {
        try {
            // Thử với tên mới trước
            try {
                return ItemFlag.valueOf(modernName);
            } catch (IllegalArgumentException e) {
                // Thử với tên cũ
                return ItemFlag.valueOf(legacyName);
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể tìm item flag: " + modernName + "/" + legacyName);
            return ItemFlag.HIDE_ATTRIBUTES; // Safe fallback
        }
    }

    /**
     * Áp dụng potion effect một cách an toàn
     * @param player Player nhận effect
     * @param effectType PotionEffectType
     * @param duration Thời gian (ticks)
     * @param amplifier Cấp độ effect (0 = level 1)
     * @param ambient Có phải ambient effect không
     * @param particles Hiển thị particles không
     * @return true nếu áp dụng thành công
     */
    public static boolean applyPotionEffect(org.bukkit.entity.Player player, PotionEffectType effectType,
                                          int duration, int amplifier, boolean ambient, boolean particles) {
        if (player == null || effectType == null) {
            return false;
        }

        try {
            org.bukkit.potion.PotionEffect effect;
            if (PRE_1_13) {
                // Phiên bản 1.12.2: không có icon parameter
                effect = new org.bukkit.potion.PotionEffect(effectType, duration, amplifier, ambient, particles);
            } else {
                // Phiên bản 1.13+: có icon parameter
                try {
                    effect = new org.bukkit.potion.PotionEffect(effectType, duration, amplifier, ambient, particles, true);
                } catch (Exception e) {
                    // Fallback nếu constructor mới không hoạt động
                    effect = new org.bukkit.potion.PotionEffect(effectType, duration, amplifier, ambient, particles);
                }
            }

            return player.addPotionEffect(effect);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể áp dụng potion effect: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Remove potion effect một cách an toàn
     * @param player Player cần remove effect
     * @param effectType PotionEffectType cần remove
     * @return true nếu remove thành công
     */
    public static boolean removePotionEffect(org.bukkit.entity.Player player, PotionEffectType effectType) {
        if (player == null || effectType == null) {
            return false;
        }

        try {
            player.removePotionEffect(effectType);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể remove potion effect: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Kiểm tra player có effect không
     * @param player Player cần kiểm tra
     * @param effectType PotionEffectType cần kiểm tra
     * @return true nếu player có effect này
     */
    public static boolean hasEffect(org.bukkit.entity.Player player, PotionEffectType effectType) {
        if (player == null || effectType == null) {
            return false;
        }

        try {
            return player.hasPotionEffect(effectType);
        } catch (Exception e) {
            return false;
        }
    }
}