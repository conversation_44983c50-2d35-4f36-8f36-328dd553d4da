package com.hongminh54.storage.Events;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.stream.Collectors;

import org.bukkit.Bukkit;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.CacheManager;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.StatsManager;

public class BlockBreakEvent_ implements Listener {
    
    // Cache để tránh thông báo kho đầy quá thường xuyên
    private static final ConcurrentHashMap<String, Long> storageFullNotificationCache = new ConcurrentHashMap<>();
    
    // Thời gian giữa các thông báo kho đầy (giây)
    private static final long NOTIFICATION_COOLDOWN = TimeUnit.SECONDS.toMillis(30);
    
    // Cấu hình hiệu ứng
    private boolean effectsEnabled;
    private int maxParticleCount;
    private boolean autoPickupEnabled;
    private boolean cancelDropEnabled;
    private boolean sendMessages;
    
    // Cache thời gian hiệu ứng cuối của mỗi người chơi
    private static final ConcurrentHashMap<String, Long> lastParticleEffectTime = new ConcurrentHashMap<>();
    // Khoảng thời gian tối thiểu giữa các hiệu ứng (milliseconds)
    private static final long PARTICLE_EFFECT_COOLDOWN = 800;
    
    // Thêm cache để kiểm soát tần suất đào block
    private static final ConcurrentHashMap<UUID, Long> lastBreakTime = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<UUID, Integer> breakCount = new ConcurrentHashMap<>();
    
    // Thêm biến để đếm số lượng block đã đào và kích hoạt lưu dữ liệu
    private static final ConcurrentHashMap<UUID, Integer> mineCountSinceLastSave = new ConcurrentHashMap<>();
    private static final int SAVE_THRESHOLD = 250; // Tăng ngưỡng lưu để giảm số lần I/O và tránh backup liên tục
    
    // Constructor để khởi tạo cấu hình ngay khi class được tạo
    public BlockBreakEvent_() {
        loadConfig();
        // Đăng ký task dọn dẹp cache
        scheduleCacheCleanup();
    }
    
    // Tải lại cấu hình khi admin reload plugin
    public void loadConfig() {
        FileConfiguration config = File.getConfig();

        // Đọc cấu hình từ file config
        effectsEnabled = config.getBoolean("settings.effects_enabled", true);
        maxParticleCount = config.getInt("settings.max_particle_count", 15);
        autoPickupEnabled = config.getBoolean("settings.auto_pickup", true);
        cancelDropEnabled = config.getBoolean("settings.block_break.cancel_drop", true);
        sendMessages = config.getBoolean("settings.block_break.send_messages", false);

        // Ghi log thông tin cấu hình khi chạy ở chế độ debug
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("BlockBreakEvent_ đã tải cấu hình: " +
                "effects=" + effectsEnabled +
                ", maxParticles=" + maxParticleCount +
                ", autoPickup=" + autoPickupEnabled +
                ", cancelDrop=" + cancelDropEnabled +
                ", sendMessages=" + sendMessages +
                ", chatty_miner=" + config.getBoolean("settings.chatty_miner", true) +
                ", chatty_miner_rate=" + config.getInt("settings.chatty_miner_rate", 15) +
                ", count_mined_blocks=" + config.getBoolean("settings.count_mined_blocks", true));
        }
    }
    
    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onBlockBreak(BlockBreakEvent event) {
        // Các kiểm tra quan trọng cần xử lý ngay trong luồng chính
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        UUID playerUUID = player.getUniqueId();
        Block block = event.getBlock();
        
        // Kiểm tra xem toggle có được bật không - nếu được bật thì BlockBreak.java sẽ xử lý
        // Điều này ngăn chặn việc xử lý khối đã đào hai lần
        if (MineManager.toggle.containsKey(player) && MineManager.toggle.get(player)) {
            return;
        }
        
        // Kiểm tra cooldown để tránh lag server từ việc đào quá nhanh
        long now = System.currentTimeMillis();
        if (lastBreakTime.containsKey(playerUUID)) {
            long lastTime = lastBreakTime.get(playerUUID);
            long elapsed = now - lastTime;
            
            // Giảm thời gian cooldown khi kiểm tra - sử dụng CacheManager
            if (elapsed < CacheManager.getBlockBreakCooldown() / 8) {
                // Chỉ hủy nếu thời gian giữa các lần đào quá gần nhau
                // Không hủy event nếu ko cần thiết
                return;
            }
        }
        
        // Cập nhật thời gian đào gần nhất
        lastBreakTime.put(playerUUID, now);
        
        // Tăng số đếm block đã phá để kiểm soát tốc độ đào
        int count = breakCount.getOrDefault(playerUUID, 0) + 1;
        breakCount.put(playerUUID, count);
        
        // Kiểm tra giới hạn số lượng block đào trong khoảng thời gian
        if (count > CacheManager.getMaxBreaks()) {
            // Chỉ hủy mỗi lần thứ 10 khi vượt quá giới hạn (thay vì mỗi lần thứ 5)
            if (count % 10 == 0) {
                // Không hủy event, chỉ làm chậm lại (logging thay vì hủy)
                if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                    Storage.getStorage().getLogger().fine("Phát hiện đào quá nhanh: " + player.getName() + " - " + count + " blocks");
                }
            }
        }
        
        // Không can thiệp vào lá cây và gỗ - để AxeEnchantListener xử lý
        String blockType = block.getType().name().toUpperCase();
        if (blockType.contains("LOG") ||
            blockType.contains("WOOD") ||
            blockType.endsWith("_STEM") ||
            blockType.contains("STRIPPED") ||
            blockType.contains("LEAVES") ||
            blockType.equals("LEAVES") ||
            blockType.equals("LEAVES_2")) {
            return; // Không xử lý gỗ và lá cây
        }

        // Kiểm tra khối có phải là khối cần thu thập không - sử dụng cache
        String blockKey;

        // Xác định khóa block cho Minecraft 1.16.5+
        blockKey = block.getType().toString() + ";0";

        boolean isMineable = CacheManager.isMineable(blockKey, k -> MineManager.checkBreak(block));

        if (!isMineable) {
            return;
        }
        
        // Lấy loại tài nguyên từ khối
        String resource = MineManager.getDrop(block);
        if (resource == null) {
            return;
        }
        
        // Kiểm tra xem plugin có đang bật tự động thu thập không
        if (!autoPickupEnabled) {
            return;
        }
        
        // Kiểm tra người chơi có bật tự động thu thập không
        Boolean isAutoPickup = CacheManager.getPlayerAttribute(player, "auto_pickup", null);
        if (isAutoPickup == null) {
            isAutoPickup = MineManager.isAutoPickup(player);
            CacheManager.setPlayerAttribute(player, "auto_pickup", isAutoPickup);
        }
        
        if (!isAutoPickup) {
            return;
        }
        
        // Kiểm tra quyền
        if (!player.hasPermission("storage.autopickup")) {
            return;
        }
        
        // Kiểm tra số lượng tài nguyên hiện tại và giới hạn - chỉ chạy một lần
        // trong luồng chính để tránh thay đổi dữ liệu không đồng bộ
        int currentAmount = MineManager.getPlayerBlock(player, resource);
        int maxStorage = MineManager.getMaxBlock(player);
        
        // Nếu đã đạt giới hạn, thông báo và thoát
        if (currentAmount >= maxStorage) {
            String playerKey = player.getName() + "_" + resource;
            Long lastNotification = storageFullNotificationCache.get(playerKey);
            
            // Chỉ thông báo nếu đã qua thời gian chờ
            if (lastNotification == null || now - lastNotification > NOTIFICATION_COOLDOWN) {
                // Cập nhật cache trước
                storageFullNotificationCache.put(playerKey, now);
                
                // Di chuyển thông báo vào luồng bất đồng bộ
                Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                    String materialName = CacheManager.getMaterialDisplayName(resource);
                    player.sendMessage(Chat.colorize(File.getMessage().getString("user.storage.full", "&c&l[Kho Khoáng Sản] &fKho của bạn đã đầy")));
                    player.sendMessage(Chat.colorize("&7Vật phẩm: &f" + materialName + " &7- Số lượng: &f" + currentAmount + "&7/&f" + maxStorage));
                });
            }
            return;
        }
        
        // Thu thập kinh nghiệm trước khi hủy drop - xử lý trong luồng chính
        int exp = event.getExpToDrop();
        event.setExpToDrop(0);
        player.giveExp(exp);
        
        // Hủy drop từ khối - xử lý trong luồng chính
        if (cancelDropEnabled) {
            try {
                // Phương pháp 1: Sử dụng setDropItems (1.12.1 trở lên)
                event.setDropItems(false);
                
                // Phương pháp 2: Xóa danh sách drops (phương pháp bổ sung cho tất cả phiên bản)
                if (block.getDrops() != null && !block.getDrops().isEmpty()) {
                    block.getDrops().clear();
                }
                
                // Phương pháp 3: Thêm metadata để đánh dấu block không nên drop vật phẩm
                block.setMetadata("NoDrops", new org.bukkit.metadata.FixedMetadataValue(Storage.getStorage(), true));
                
                // Phương pháp 4: Thu gom các item đã drop ngay lập tức với phạm vi rộng hơn
                Bukkit.getScheduler().runTask(Storage.getStorage(), () -> {
                    // Thu thập tất cả vật phẩm gần block vừa bị phá với phạm vi lớn hơn (2.0 thay vì 1.5)
                    for (org.bukkit.entity.Entity entity : block.getWorld().getNearbyEntities(
                            block.getLocation().add(0.5, 0.5, 0.5), 2.0, 2.0, 2.0)) {
                        if (entity instanceof org.bukkit.entity.Item) {
                            org.bukkit.entity.Item item = (org.bukkit.entity.Item) entity;
                            // Tăng thời gian sống của item để bắt được nhiều hơn (< 3 tick thay vì < 2 tick)
                            if (item.getTicksLived() < 3) {
                                item.remove();
                            }
                        }
                    }
                });
            } catch (Exception ex) {
                // Xử lý trường hợp lỗi, hầu hết do phiên bản Minecraft
                Storage.getStorage().getLogger().warning("Không thể hủy drop từ block, có thể do phiên bản Minecraft: " + ex.getMessage());
                // Cố gắng sử dụng phương pháp thay thế
                try {
                    // Thủ công xóa drops ngay lập tức, không đợi tick tiếp theo
                    // Thu thập tất cả vật phẩm gần block vừa bị phá
                    for (org.bukkit.entity.Entity entity : block.getWorld().getNearbyEntities(
                            block.getLocation().add(0.5, 0.5, 0.5), 2.0, 2.0, 2.0)) {
                        if (entity instanceof org.bukkit.entity.Item) {
                            org.bukkit.entity.Item item = (org.bukkit.entity.Item) entity;
                            // Tăng thời gian sống của item để bắt được nhiều hơn
                            if (item.getTicksLived() < 3) {
                                item.remove();
                            }
                        }
                    }
                } catch (Exception ignored) {
                    // Bỏ qua nếu có lỗi phụ
                }
            }
        }
        
        // Thu thập tài nguyên - xử lý trong luồng chính
        final boolean addSuccess = MineManager.addBlockAmount(player, resource, 1);
        
        // Nếu không thành công, không cần xử lý gì thêm
        if (!addSuccess) {
            return;
        }
        
        // Tăng bộ đếm số block đã đào cho người chơi
        int mineCount = mineCountSinceLastSave.getOrDefault(playerUUID, 0) + 1;
        mineCountSinceLastSave.put(playerUUID, mineCount);
        
        // Nếu đạt ngưỡng, lưu dữ liệu để tránh mất khi rollback
        if (mineCount >= SAVE_THRESHOLD) {
            mineCountSinceLastSave.put(playerUUID, 0);
            // Lưu dữ liệu người chơi bất đồng bộ
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                try {
                    // Chỉ log ở chế độ DEBUG hoặc mỗi 5 lần lưu để giảm spam
                    if (Storage.getStorage().getLogger().isLoggable(Level.FINE) || 
                        System.currentTimeMillis() % 5 == 0) {
                        Storage.getStorage().getLogger().fine("Đang lưu dữ liệu của " + player.getName() + " sau khi đào " + SAVE_THRESHOLD + " khối");
                    }
                    MineManager.savePlayerDataAsync(player);
                } catch (Exception ex) {
                    Storage.getStorage().getLogger().warning("Lỗi khi lưu dữ liệu sau khi đào nhiều khối: " + ex.getMessage());
                }
            });
        }
        
        // Ghi nhận thống kê khai thác - chỉ khi được bật trong config
        boolean countMinedBlocks = File.getConfig().getBoolean("settings.count_mined_blocks", true);
        if (countMinedBlocks) {
            try {
                StatsManager.recordMining(player, 1);
            } catch (Exception e) {
                Storage.getStorage().getLogger().log(Level.WARNING,
                    "Lỗi khi ghi nhận thống kê khai thác cho " + player.getName() + ": " + e.getMessage(), e);
            }
        }

        final String resourceFinal = resource;
        final int currentAmountFinal = currentAmount;

        // Chatty miner - hiển thị tin nhắn khai thác ngẫu nhiên
        boolean chattyMinerEnabled = File.getConfig().getBoolean("settings.chatty_miner", true);
        if (chattyMinerEnabled) {
            int chattyMinerRate = File.getConfig().getInt("settings.chatty_miner_rate", 15);
            if (chattyMinerRate > 0 && Math.random() < (1.0 / chattyMinerRate)) {
                // Hiển thị tin nhắn khai thác
                Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                    try {
                        String materialName = CacheManager.getMaterialDisplayName(resourceFinal);
                        String chattyMessage = File.getMessage().getString("user.mining.chatty_message",
                            "&7[&e⛏&7] &f{player} &7đã khai thác &a{material}&7!");
                        chattyMessage = chattyMessage.replace("{player}", player.getName())
                                                   .replace("{material}", materialName);
                        player.sendMessage(Chat.colorize(chattyMessage));
                    } catch (Exception e) {
                        // Bỏ qua lỗi chatty miner
                    }
                });
            }
        }

        // Các hoạt động không bắt buộc xử lý đồng bộ (thông báo, hiệu ứng, thống kê)
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
            });
            
            // Hiệu ứng cần chạy trong luồng chính do sử dụng API Bukkit không an toàn với luồng phụ
        if (effectsEnabled) {
            try {
                // Kiểm tra thời gian giữa các hiệu ứng
                String playerName = player.getName();
                Long lastEffect = lastParticleEffectTime.get(playerName);
                
                // Chỉ hiển thị hiệu ứng nếu đã qua thời gian chờ
                if (lastEffect == null || now - lastEffect > PARTICLE_EFFECT_COOLDOWN) {
                    // Cập nhật thời gian hiệu ứng cuối
                    lastParticleEffectTime.put(playerName, now);
                    
                    // Gọi hiệu ứng với số lượng hạt được tối ưu
                    playCollectEffect(player, block.getLocation(), maxParticleCount);
                }
            } catch (Exception e) {
                Storage.getStorage().getLogger().warning("Lỗi khi tạo hiệu ứng thu thập: " + e.getMessage());
            }
        }
    }

    /**
     * Dọn dẹp cache định kỳ - Đã được chuyển sang CacheManager để tránh trùng lặp
     */
    public void scheduleCacheCleanup() {
        // Chỉ đặt lịch trình reset bộ đếm đào block
        int resetInterval = CacheManager.getResetInterval();
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), this::resetBreakCounters, 20 * resetInterval, 20 * resetInterval);

        // Đặt lịch trình dọn dẹp cache riêng cho BlockBreakEvent (chỉ notification và effect cache)
        long cleanupInterval = File.getConfig().getLong("cache.cooldown.cleanup_interval", 6000);
        Bukkit.getScheduler().runTaskTimerAsynchronously(Storage.getStorage(), this::cleanupLocalCache, cleanupInterval, cleanupInterval);
    }

    /**
     * Dọn dẹp cache cục bộ của BlockBreakEvent
     */
    private void cleanupLocalCache() {
        try {
            // Dọn dẹp thông báo kho đầy cũ
            long now = System.currentTimeMillis();
            List<String> keysToRemove = storageFullNotificationCache.entrySet().stream()
                    .filter(entry -> now - entry.getValue() > NOTIFICATION_COOLDOWN * 2)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            for (String key : keysToRemove) {
                storageFullNotificationCache.remove(key);
            }

            // Dọn dẹp thời gian hiệu ứng cuối
            List<String> effectKeysToRemove = lastParticleEffectTime.entrySet().stream()
                    .filter(entry -> now - entry.getValue() > PARTICLE_EFFECT_COOLDOWN * 5)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            for (String key : effectKeysToRemove) {
                lastParticleEffectTime.remove(key);
            }

            // Log thông tin ở chế độ debug
            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                Storage.getStorage().getLogger().fine(
                    "BlockBreakEvent local cache cleanup: Removed " + keysToRemove.size() +
                    " storage notification entries and " + effectKeysToRemove.size() + " effect entries"
                );
            }
        } catch (Exception e) {
            // Bỏ qua lỗi khi dọn dẹp cache
            Storage.getStorage().getLogger().warning("Lỗi khi dọn dẹp local cache: " + e.getMessage());
        }
    }
    
    /**
     * Đặt lại bộ đếm đào block cho tất cả người chơi
     */
    private void resetBreakCounters() {
        try {
            breakCount.clear();
            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                Storage.getStorage().getLogger().fine("Đã đặt lại bộ đếm block cho tất cả người chơi");
            }
        } catch (Exception e) {
            // Bỏ qua lỗi khi đặt lại bộ đếm
        }
    }
    
    /**
     * Phát hiệu ứng thu thập tài nguyên
     * @param player Người chơi
     * @param location Vị trí khối
     * @param maxParticleCount Số lượng hạt tối đa
     */
    private void playCollectEffect(Player player, org.bukkit.Location location, int maxParticleCount) {
        // Kiểm tra xem có nhiều người chơi đang đào cùng lúc không để điều chỉnh hiệu ứng
        int onlinePlayers = Bukkit.getOnlinePlayers().size();
        int adjustedParticleLimit;
        
        // Điều chỉnh số lượng hạt dựa trên số người chơi online
        if (onlinePlayers > 20) {
            adjustedParticleLimit = 3; // Rất ít hạt nếu server đông
        } else if (onlinePlayers > 10) {
            adjustedParticleLimit = 5; // Ít hạt nếu có nhiều người
        } else {
            adjustedParticleLimit = 8; // Số lượng bình thường nếu ít người
        }
        
        // Giới hạn số lượng hạt xuống mức thấp hơn để tăng hiệu suất
        int particleLimit = Math.min(maxParticleCount, adjustedParticleLimit);
        
        // Đọc cấu hình hiệu ứng từ config nếu có
        String effectConfig = File.getConfig().getString("effects.collect.particle", "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:3");
        String soundConfig = File.getConfig().getString("effects.collect.sound", "ENTITY_ITEM_PICKUP:0.2:0.8");
        
        // Xử lý hiệu ứng hạt
        if (effectConfig != null && !effectConfig.isEmpty()) {
            try {
                String[] parts = effectConfig.split(":");
                org.bukkit.Particle particleType;
                try {
                    particleType = org.bukkit.Particle.valueOf(parts[0]);
                } catch (IllegalArgumentException e) {
                    // Fallback to a safe particle if not found - tương thích đa phiên bản
                    try {
                        particleType = org.bukkit.Particle.HAPPY_VILLAGER;
                    } catch (NoSuchFieldError ex) {
                        particleType = org.bukkit.Particle.valueOf("VILLAGER_HAPPY");
                    }
                }
                
                double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.3;
                double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.3;
                double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.3;
                double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.05;
                int count = parts.length > 5 ? Integer.parseInt(parts[5]) : 3;
                
                // Giới hạn số lượng hạt để tránh ảnh hưởng hiệu suất
                count = Math.min(count, particleLimit);
                
                // Sử dụng vị trí khối + 0.5 để hiệu ứng xuất hiện ở giữa khối
                location = location.clone().add(0.5, 0.5, 0.5);
                
                // Giảm tần suất phát hiệu ứng khi đào nhanh
                if (player.getHealth() > 0 && player.getGameMode() == org.bukkit.GameMode.SURVIVAL) {
                // Chỉ hiển thị hiệu ứng cho người chơi đào - tối ưu bằng cách không hiển thị cho các người chơi khác
                player.spawnParticle(particleType, location, count, offsetX, offsetY, offsetZ, speed);
                }
            } catch (Exception e) {
                // Bỏ qua nếu có lỗi khi tạo hiệu ứng
            }
        }
        
        // Xử lý âm thanh - chỉ phát 50% thời gian để giảm lag
        if (soundConfig != null && !soundConfig.isEmpty() && Math.random() > 0.5) {
            try {
                String[] parts = soundConfig.split(":");
                String soundName = parts[0];
                float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 0.2f;
                float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 0.8f;

                // Giảm âm lượng khi đào nhanh và sử dụng SoundCompatibility
                com.hongminh54.storage.Utils.SoundCompatibility.playSound(player, soundName, volume * 0.7f, pitch);
            } catch (Exception e) {
                // Bỏ qua nếu có lỗi khi phát âm thanh
            }
        }
    }

    /**
     * Điều chỉnh số lượng hạt hiệu ứng dựa trên số lượng người chơi và TPS hiện tại
     * Hỗ trợ tính năng tối ưu hóa cho máy chủ đông người
     * 
     * @param playerCount Số lượng người chơi hiện tại
     * @param currentTps TPS hiện tại của server
     * @param playerThreshold Ngưỡng người chơi để kích hoạt tối ưu hóa
     * @param reductionFactor Hệ số giảm (0.0-1.0)
     */
    public void adjustParticleEffects(int playerCount, double currentTps, int playerThreshold, double reductionFactor) {
        // Chỉ giảm hiệu ứng nếu số người chơi vượt ngưỡng
        if (playerCount >= playerThreshold || currentTps < 16.0) {
            // Lưu giá trị hiện tại trước khi thay đổi
            int originalParticleCount = this.maxParticleCount;
            
            // Giảm số lượng hạt tối đa theo hệ số giảm
            this.maxParticleCount = Math.max(1, (int)(originalParticleCount * reductionFactor));
            
            // Ghi log nếu cần
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info(
                    String.format("Đã điều chỉnh hiệu ứng hạt từ %d xuống %d (Người chơi: %d, TPS: %.2f)",
                    originalParticleCount, this.maxParticleCount, playerCount, currentTps));
            }
        } else {
            // Khôi phục về giá trị mặc định từ cấu hình
            this.maxParticleCount = File.getConfig().getInt("settings.max_particle_count", 15);
        }
    }
} 