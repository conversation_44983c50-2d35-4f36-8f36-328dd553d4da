package com.hongminh54.storage.Listeners;

import java.util.Random;

import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.ExperienceOrb;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.MetadataValue;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Manager.HoeEnchantManager;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.BiomeCompatibility;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.ParticleEffect;
import com.hongminh54.storage.Utils.PotionCompatibility;
import com.hongminh54.storage.Utils.SoundManager;
import com.hongminh54.storage.WorldGuard.WorldGuard;

/**
 * Lớp xử lý sự kiện phá block với cuốc có phù phép
 */
public class HoeEnchantListener implements Listener {
    
    private final Random random = new Random();
    private final NMSAssistant nmsAssistant = new NMSAssistant();

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void onBlockBreak(@NotNull BlockBreakEvent e) {
        Player player = e.getPlayer();
        Block block = e.getBlock();
        ItemStack handItem = player.getInventory().getItemInMainHand();
        
        // Kiểm tra điều kiện cơ bản
        if (player.getGameMode() == GameMode.CREATIVE) {
            return;
        }
        
        if (handItem == null || handItem.getType() == Material.AIR) {
            return;
        }
        
        if (!HoeEnchantManager.isHoe(handItem)) {
            return;
        }
        
        // Kiểm tra xem block có phải là cây trồng không
        boolean debug = Storage.getStorage().isDebug();
        if (!HoeEnchantManager.isCrop(block)) {
            if (debug) {
                Storage.getStorage().getLogger().info("HoeEnchant: Block " + block.getType().name() + " không phải là cây trồng");
            }
            return;
        }

        if (debug) {
            Storage.getStorage().getLogger().info("HoeEnchant: Phát hiện cây trồng " + block.getType().name() + " tại " + block.getLocation());
        }
        
        // Kiểm tra các điều kiện khác
        if (Storage.isWorldGuardInstalled() && !WorldGuard.handleForLocation(player, block.getLocation())) {
            return;
        }
        
        if (File.getConfig().getBoolean("prevent_rebreak") && isPlacedBlock(block)) {
            return;
        }
        
        if (File.getConfig().contains("blacklist_world") && 
            File.getConfig().getStringList("blacklist_world").contains(player.getWorld().getName())) {
            return;
        }
        
        // Đây là cây trồng, kiểm tra từng phù phép
        
        // Kiểm tra phù phép Nông Dân Chuyên Nghiệp (Farmer's Touch)
        int farmersTouchLevel = HoeEnchantManager.getEnchantLevel(handItem, HoeEnchantManager.FARMERS_TOUCH);
        if (farmersTouchLevel > 0) {
            if (debug) {
                Storage.getStorage().getLogger().info("HoeEnchant: Bắt đầu xử lý phù phép Nông Dân Chuyên Nghiệp với cấp độ " + farmersTouchLevel);
            }
            
            // Xử lý tự động trồng lại
            boolean autoReplanted = HoeEnchantManager.handleFarmersTouch(player, block, farmersTouchLevel);
            
            if (autoReplanted) {
                playEffects(player, block.getLocation(), "farmers_touch");
                sendEffectMessage(player, farmersTouchLevel, "farmers_touch");
            }
        }
        
        // Kiểm tra phù phép Đất Màu Mỡ (Fertile Soil)
        int fertileSoilLevel = HoeEnchantManager.getEnchantLevel(handItem, HoeEnchantManager.FERTILE_SOIL);
        if (fertileSoilLevel > 0) {
            if (debug) {
                Storage.getStorage().getLogger().info("HoeEnchant: Bắt đầu xử lý phù phép Đất Màu Mỡ với cấp độ " + fertileSoilLevel);
            }
            
            boolean soilEnhanced = HoeEnchantManager.handleFertileSoil(player, block, fertileSoilLevel);
            
            if (soilEnhanced) {
                playEffects(player, block.getLocation(), "fertile_soil");
                sendEffectMessage(player, fertileSoilLevel, "fertile_soil");
            }
        }
        
        // Kiểm tra phù phép Kinh Nghiệm Nông Dân (Farmer's Wisdom)
        int farmersWisdomLevel = HoeEnchantManager.getEnchantLevel(handItem, HoeEnchantManager.FARMERS_WISDOM);
        if (farmersWisdomLevel > 0) {
            if (debug) {
                Storage.getStorage().getLogger().info("HoeEnchant: Bắt đầu xử lý phù phép Kinh Nghiệm Nông Dân với cấp độ " + farmersWisdomLevel);
            }
            
            // Tính toán lượng kinh nghiệm bổ sung
            int baseXp = 1; // Kinh nghiệm cơ bản khi thu hoạch cây trồng
            int bonusXp = (int) (baseXp * (farmersWisdomLevel * 0.5)); // Tăng 50% mỗi cấp độ
            
            // Tạo kinh nghiệm ở vị trí block
            if (bonusXp > 0) {
                Location loc = block.getLocation().add(0.5, 0.5, 0.5);
                loc.getWorld().spawn(loc, ExperienceOrb.class).setExperience(bonusXp);
                
                playEffects(player, block.getLocation(), "farmers_wisdom");
                sendEffectMessage(player, farmersWisdomLevel, "farmers_wisdom");
            }
        }
        
        // Kiểm tra phù phép Tái Sinh (Regeneration)
        int regenerationLevel = HoeEnchantManager.getEnchantLevel(handItem, HoeEnchantManager.REGENERATION);
        if (regenerationLevel > 0) {
            if (debug) {
                Storage.getStorage().getLogger().info("HoeEnchant: Bắt đầu xử lý phù phép Tái Sinh với cấp độ " + regenerationLevel);
            }
            
            // Có cơ hội hồi phục độ bền của cuốc
            if (handItem.getDurability() > 0 && random.nextInt(100) < (5 * regenerationLevel)) { // 5/10/15% cơ hội tùy cấp độ
                short newDurability = (short) Math.max(0, handItem.getDurability() - (2 * regenerationLevel));
                handItem.setDurability(newDurability);

                playEffects(player, player.getLocation(), "regeneration");
                sendEffectMessage(player, regenerationLevel, "regeneration");
            }

            // Tính năng mới: Áp dụng regeneration effect cho player trong biome phù hợp
            processPlayerRegenerationEffect(player, block, regenerationLevel);
        }
    }
    
    /**
     * Phát hiệu ứng âm thanh và hạt khi kích hoạt phù phép
     * 
     * @param player Người chơi sử dụng phù phép
     * @param location Vị trí khối bị phá
     * @param enchantType Loại phù phép
     */
    private void playEffects(Player player, Location location, String enchantType) {
        // Xử lý âm thanh sử dụng SoundManager tương thích đa phiên bản
        String soundConfig = File.getEnchants().getString("hoe_enchant." + enchantType + ".sound", "ENTITY_EXPERIENCE_ORB_PICKUP:0.3:1.2");
        if (soundConfig != null && !soundConfig.isEmpty()) {
            try {
                String[] soundParts = soundConfig.split(":");
                String soundName = soundParts[0];
                float volume = soundParts.length > 1 ? Float.parseFloat(soundParts[1]) : 0.3f;
                float pitch = soundParts.length > 2 ? Float.parseFloat(soundParts[2]) : 1.2f;

                // Sử dụng SoundManager để phát âm thanh tương thích đa phiên bản
                SoundManager.playSound(player, soundName, volume, pitch);
            } catch (Exception e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Lỗi khi phát âm thanh " + enchantType + ": " + e.getMessage());
                }
            }
        }
        
        // Xử lý hiệu ứng hạt sử dụng ParticleEffect tương thích đa phiên bản
        String particleConfig = File.getEnchants().getString("hoe_enchant." + enchantType + ".particle", "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:5");
        if (particleConfig != null && !particleConfig.isEmpty()) {
            try {
                String[] particleParts = particleConfig.split(":");
                String particleName = particleParts[0];
                double offsetX = particleParts.length > 1 ? Double.parseDouble(particleParts[1]) : 0.3;
                double offsetY = particleParts.length > 2 ? Double.parseDouble(particleParts[2]) : 0.3;
                double offsetZ = particleParts.length > 3 ? Double.parseDouble(particleParts[3]) : 0.3;
                double speed = particleParts.length > 4 ? Double.parseDouble(particleParts[4]) : 0.05;
                int count = particleParts.length > 5 ? Integer.parseInt(particleParts[5]) : 5;

                // Sử dụng ParticleEffect để tạo hiệu ứng tương thích đa phiên bản
                ParticleEffect.spawnParticle(location, particleName, count, offsetX, offsetY, offsetZ, speed);
            } catch (Exception e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Lỗi khi xử lý cấu hình hiệu ứng hạt " + enchantType + ": " + e.getMessage());
                }
            }
        }
    }
    
    // Các method isParticleEnumSupported() và convertParticleName() đã được thay thế
    // bằng ParticleEffect compatibility class

    /**
     * Xử lý hiệu ứng regeneration cho player khi sử dụng hoe enchant
     * Tính năng mới sử dụng PotionCompatibility và BiomeCompatibility
     */
    private void processPlayerRegenerationEffect(Player player, Block block, int regenerationLevel) {
        // Kiểm tra config có bật tính năng này không
        if (!File.getEnchants().getBoolean("hoe_enchant.player_regeneration.enabled", false)) {
            return;
        }

        // Kiểm tra biome có phù hợp không (plains, forest có hiệu quả tốt hơn)
        org.bukkit.block.Biome biome = BiomeCompatibility.getBiomeSafely(block.getLocation());
        boolean isFarmingBiome = BiomeCompatibility.isForestBiome(biome) ||
                                (biome != null && biome.name().toLowerCase().contains("plains"));

        // Tính toán cơ hội regeneration dựa trên level và biome
        int baseChance = File.getEnchants().getInt("hoe_enchant.player_regeneration.base_chance", 3); // 3%
        int levelBonus = regenerationLevel * 2; // +2% per level
        int biomeBonus = isFarmingBiome ? File.getEnchants().getInt("hoe_enchant.player_regeneration.biome_bonus", 5) : 0; // +5% trong farming biome
        int totalChance = baseChance + levelBonus + biomeBonus;

        // Random check
        if (new java.util.Random().nextInt(100) >= totalChance) {
            return;
        }

        // Áp dụng regeneration effect
        org.bukkit.potion.PotionEffectType regenType = PotionCompatibility.getCompatiblePotionEffectType("REGENERATION", "REGENERATION");
        if (regenType != null) {
            int duration = File.getEnchants().getInt("hoe_enchant.player_regeneration.duration", 40); // 2 seconds (40 ticks)
            int amplifier = File.getEnchants().getInt("hoe_enchant.player_regeneration.amplifier", 0); // Level 1

            boolean success = PotionCompatibility.addPotionEffectSafely(player, regenType, duration, amplifier);

            if (success) {
                // Hiệu ứng đặc biệt cho regeneration
                playEffects(player, player.getLocation(), "player_regeneration");

                // Thông báo
                String message = File.getEnchants().getString("hoe_enchant.player_regeneration.message",
                    "&a🌾 &eĐất đai màu mỡ ban phước lành cho bạn!");
                if (message != null && !message.isEmpty()) {
                    player.sendMessage(Chat.colorizewp(message));
                }

                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("HoeEnchant: Áp dụng regeneration cho " + player.getName() +
                        " (biome: " + (biome != null ? biome.name() : "unknown") + ", farming: " + isFarmingBiome + ")");
                }
            }
        }
    }
    
    /**
     * Gửi thông báo hiệu ứng khi sử dụng phù phép
     * 
     * @param player Người chơi nhận thông báo
     * @param level Cấp độ phù phép
     * @param enchantType Loại phù phép
     */
    private void sendEffectMessage(Player player, int level, String enchantType) {
        if (!File.getEnchants().getBoolean("hoe_enchant." + enchantType + ".effect_message.enabled", true)) {
            return;
        }

        String format = File.getEnchants().getString("hoe_enchant." + enchantType + ".effect_message.format",
                                                 "&a✿ &ePhù phép %enchant% %level% &akích hoạt!");
        
        if (format != null && !format.isEmpty()) {
            String enchantName = getEnchantDisplayName(enchantType);
            String message = format.replace("%level%", getRomanLevel(level))
                                 .replace("%enchant%", enchantName);
            
            player.sendMessage(Chat.colorizewp(message));
        }
    }
    
    /**
     * Lấy tên hiển thị cho loại phù phép
     */
    private String getEnchantDisplayName(String enchantType) {
        switch (enchantType) {
            case HoeEnchantManager.FARMERS_TOUCH: return "Nông Dân Chuyên Nghiệp";
            case HoeEnchantManager.FERTILE_SOIL: return "Đất Màu Mỡ";
            case HoeEnchantManager.FARMERS_WISDOM: return "Kinh Nghiệm Nông Dân";
            case HoeEnchantManager.REGENERATION: return "Tái Sinh";
            default: return enchantType;
        }
    }
    
    /**
     * Chuyển đổi cấp độ số thành chữ số La Mã
     */
    private String getRomanLevel(int level) {
        switch (level) {
            case 1: return "I";
            case 2: return "II";
            case 3: return "III";
            default: return String.valueOf(level);
        }
    }
    
    /**
     * Kiểm tra xem block có phải do người chơi đặt không
     */
    private boolean isPlacedBlock(Block block) {
        for (MetadataValue meta : block.getMetadata("placed")) {
            if (meta.asBoolean()) {
                return true;
            }
        }
        return false;
    }
} 