package com.hongminh54.storage.Listeners;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.MetadataValue;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Manager.AxeEnchantManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.BiomeCompatibility;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.ParticleEffect;
import com.hongminh54.storage.Utils.PotionCompatibility;
import com.hongminh54.storage.Utils.SoundManager;
import com.hongminh54.storage.WorldGuard.WorldGuard;

/**
 * Lớp xử lý sự kiện phá block với rìu có phù phép
 */
public class AxeEnchantListener implements Listener {

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void onBlockBreak(@NotNull BlockBreakEvent e) {
        Player player = e.getPlayer();
        Block block = e.getBlock();
        ItemStack handItem = player.getInventory().getItemInMainHand();
        
        // Kiểm tra điều kiện cơ bản
        if (player.getGameMode() == GameMode.CREATIVE) {
            return;
        }
        
        if (handItem == null || handItem.getType() == Material.AIR) {
            return;
        }
        
        if (!AxeEnchantManager.isAxe(handItem)) {
            return;
        }
        
        // Kiểm tra xem block có phải là gỗ hoặc lá cây không
        boolean isWoodBlock = AxeEnchantManager.isWood(block);
        boolean isLeafBlock = AxeEnchantManager.isLeaves(block);

        if (!isWoodBlock && !isLeafBlock) {
            return;
        }
        
        // Kiểm tra các điều kiện khác
        if (Storage.isWorldGuardInstalled() && !WorldGuard.handleForLocation(player, block.getLocation())) {
            return;
        }
        
        if (File.getConfig().getBoolean("prevent_rebreak") && isPlacedBlock(block)) {
            return;
        }
        
        if (File.getConfig().contains("blacklist_world") && 
            File.getConfig().getStringList("blacklist_world").contains(player.getWorld().getName())) {
            return;
        }
        
        // Đây là block gỗ hoặc lá cây, kiểm tra từng phù phép
        boolean debug = Storage.getStorage().isDebug();

        // Nếu là lá cây, chỉ xử lý Leaf Collector
        if (isLeafBlock && !isWoodBlock) {
            handleLeafCollectorOnly(player, block, handItem, debug, e);
            return;
        }

        // Kiểm tra và xử lý phù phép Tree Cutter (Chặt Cây) - chỉ cho gỗ
        int treeCutterLevel = AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.TREE_CUTTER);
        if (treeCutterLevel > 0 && isWoodBlock) {
            if (debug) {
                Storage.getStorage().getLogger().info("AxeEnchant: Bắt đầu xử lý phù phép Chặt Cây với cấp độ " + treeCutterLevel);
            }
            
            List<Block> blocksToBreak = AxeEnchantManager.handleTreeCutter(player, block, treeCutterLevel);
            
            // Nếu chỉ có 1 block (block chính) thì để hệ thống khác xử lý
            if (blocksToBreak.size() <= 1) {
                // Kiểm tra các phù phép khác
                processOtherEnchants(player, block, handItem);
                return;
            }
            
            if (debug) {
                Storage.getStorage().getLogger().info("Tree Cutter: Xử lý " + blocksToBreak.size() + " block");
            }
            
            // Hủy sự kiện ban đầu vì sẽ được xử lý thủ công
            e.setCancelled(true);
            
            // Xử lý khối theo cơ chế auto pickup
            handleBlockBreak(player, blocksToBreak);
            
            // Hiệu ứng âm thanh và hạt
            playEffects(player, block.getLocation(), "tree_cutter");
            
            // Thông báo hiệu ứng phá khối theo cấp độ
            sendEffectMessage(player, treeCutterLevel, blocksToBreak.size(), "tree_cutter");
            
            // Kiểm tra và xử lý phù phép Leaf Collector nếu có cùng với Tree Cutter
            int leafCollectorLevel = AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.LEAF_COLLECTOR);
            if (leafCollectorLevel > 0) {
                // Thu thập lá cây xung quanh sau khi chặt cây
                List<Block> leavesToBreak = AxeEnchantManager.handleLeafCollector(player, block, leafCollectorLevel);

                if (!leavesToBreak.isEmpty()) {
                    if (debug) {
                        Storage.getStorage().getLogger().info("Leaf Collector: Thu thập " + leavesToBreak.size() + " lá cây sau khi chặt cây");
                    }

                    // Xử lý phá lá cây
                    handleBlockBreak(player, leavesToBreak);

                    // Hiệu ứng khi thu thập lá (riêng biệt với tree cutter)
                    playEffects(player, block.getLocation(), "leaf_collector");

                    // Thông báo hiệu ứng thu thập lá
                    sendEffectMessage(player, leafCollectorLevel, leavesToBreak.size(), "leaf_collector");
                }
            }

            // Kiểm tra và xử lý phù phép Regrowth sau khi phá cây
            processRegrowth(player, block, handItem);

            // Kiểm tra và xử lý phù phép Auto Plant sau khi phá cây
            processAutoPlant(player, block, handItem);

            return;
        }
        
        // Xử lý các phù phép khác cho gỗ (không bao gồm leaf collector vì đã xử lý ở trên)
        processOtherEnchants(player, block, handItem);
    }
    
    /**
     * Xử lý Leaf Collector khi phá lá cây trực tiếp
     */
    private void handleLeafCollectorOnly(Player player, Block block, ItemStack handItem, boolean debug, BlockBreakEvent event) {
        int leafCollectorLevel = AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.LEAF_COLLECTOR);
        if (leafCollectorLevel <= 0) {
            return;
        }

        // Thu thập lá cây xung quanh khi phá lá cây trực tiếp
        List<Block> leavesToBreak = AxeEnchantManager.handleLeafCollectorFromLeaf(player, block, leafCollectorLevel);

        if (!leavesToBreak.isEmpty()) {
            if (debug) {
                Storage.getStorage().getLogger().info("Leaf Collector: Thu thập " + leavesToBreak.size() + " lá cây từ lá");
            }

            // Lưu tổng số lá cây trước khi xử lý
            int totalLeaves = leavesToBreak.size();

            // Hủy sự kiện ban đầu vì chúng ta sẽ xử lý thủ công TẤT CẢ lá cây
            event.setCancelled(true);

            // Xử lý tất cả lá cây (bao gồm cả lá cây gốc)
            handleBlockBreak(player, leavesToBreak);

            // Hiệu ứng khi thu thập lá
            playEffects(player, block.getLocation(), "leaf_collector");

            // Thông báo hiệu ứng thu thập lá (bao gồm cả lá cây gốc)
            sendEffectMessage(player, leafCollectorLevel, totalLeaves, "leaf_collector");
        }
    }

    /**
     * Xử lý các phù phép khác ngoài Tree Cutter
     */
    private void processOtherEnchants(Player player, Block block, ItemStack handItem) {
        // Xử lý phù phép Regrowth
        processRegrowth(player, block, handItem);

        // Xử lý phù phép Auto Plant
        processAutoPlant(player, block, handItem);

        // Xử lý hiệu ứng regeneration cho player (tính năng mới)
        processPlayerRegeneration(player, handItem, block);
    }
    
    /**
     * Xử lý phù phép Regrowth (Tái Sinh)
     */
    private void processRegrowth(Player player, Block block, ItemStack handItem) {
        int regrowthLevel = AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.REGROWTH);
        if (regrowthLevel > 0) {
            boolean success = AxeEnchantManager.handleRegrowth(player, block, regrowthLevel);
            
            if (success) {
                // Hiệu ứng khi tái sinh cây
                playEffects(player, block.getLocation(), "regrowth");
                
                // Thông báo hiệu ứng tái sinh cây
                String message = File.getEnchants().getString("axe_enchant.regrowth.effect_message.format",
                                    "&aTự động tái sinh cây thành công!");
                
                if (message != null && !message.isEmpty()) {
                    player.sendMessage(Chat.colorizewp(message));
                }
            }
        }
    }
    
    /**
     * Xử lý phù phép Auto Plant (Tự Trồng)
     */
    private void processAutoPlant(Player player, Block block, ItemStack handItem) {
        int autoPlantLevel = AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.AUTO_PLANT);
        if (autoPlantLevel > 0) {
            boolean success = AxeEnchantManager.handleAutoPlant(player, block);
            
            if (success) {
                // Hiệu ứng khi tự động trồng cây
                playEffects(player, block.getLocation(), "auto_plant");
                
                // Thông báo khi tự động trồng cây
                String message = File.getEnchants().getString("axe_enchant.auto_plant.effect_message.format",
                                    "&aTự động trồng cây con thành công!");
                
                if (message != null && !message.isEmpty()) {
                    player.sendMessage(Chat.colorizewp(message));
                }
            }
        }
    }

    /**
     * Xử lý hiệu ứng regeneration cho player khi sử dụng axe enchant
     * Tính năng mới sử dụng PotionCompatibility và BiomeCompatibility
     */
    private void processPlayerRegeneration(Player player, ItemStack handItem, Block block) {
        // Kiểm tra config có bật tính năng này không
        if (!File.getEnchants().getBoolean("axe_enchant.player_regeneration.enabled", false)) {
            return;
        }

        // Kiểm tra có enchant nào trên axe không
        boolean hasAnyEnchant = AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.TREE_CUTTER) > 0 ||
                               AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.LEAF_COLLECTOR) > 0 ||
                               AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.REGROWTH) > 0 ||
                               AxeEnchantManager.getEnchantLevel(handItem, AxeEnchantManager.AUTO_PLANT) > 0;

        if (!hasAnyEnchant) {
            return;
        }

        // Kiểm tra biome có phù hợp không (forest biomes có hiệu quả tốt hơn)
        org.bukkit.block.Biome biome = BiomeCompatibility.getBiomeSafely(block.getLocation());
        boolean isForestBiome = BiomeCompatibility.isForestBiome(biome);

        // Tính toán cơ hội regeneration
        int baseChance = File.getEnchants().getInt("axe_enchant.player_regeneration.base_chance", 5); // 5%
        int forestBonus = isForestBiome ? File.getEnchants().getInt("axe_enchant.player_regeneration.forest_bonus", 5) : 0; // +5% trong forest
        int totalChance = baseChance + forestBonus;

        // Random check
        if (new java.util.Random().nextInt(100) >= totalChance) {
            return;
        }

        // Áp dụng regeneration effect
        org.bukkit.potion.PotionEffectType regenType = PotionCompatibility.getCompatiblePotionEffectType("REGENERATION", "REGENERATION");
        if (regenType != null) {
            int duration = File.getEnchants().getInt("axe_enchant.player_regeneration.duration", 60); // 3 seconds (60 ticks)
            int amplifier = File.getEnchants().getInt("axe_enchant.player_regeneration.amplifier", 0); // Level 1

            boolean success = PotionCompatibility.addPotionEffectSafely(player, regenType, duration, amplifier);

            if (success) {
                // Hiệu ứng đặc biệt cho regeneration
                playEffects(player, player.getLocation(), "player_regeneration");

                // Thông báo
                String message = File.getEnchants().getString("axe_enchant.player_regeneration.message",
                    "&a🌿 &eNăng lượng từ thiên nhiên hồi phục sức khỏe của bạn!");
                if (message != null && !message.isEmpty()) {
                    player.sendMessage(Chat.colorizewp(message));
                }

                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("AxeEnchant: Áp dụng regeneration cho " + player.getName() +
                        " (biome: " + (biome != null ? biome.name() : "unknown") + ", forest: " + isForestBiome + ")");
                }
            }
        }
    }

    /**
     * Phát hiệu ứng âm thanh và hạt khi kích hoạt phù phép
     * 
     * @param player Người chơi sử dụng phù phép
     * @param location Vị trí khối bị phá
     * @param enchantType Loại phù phép
     */
    private void playEffects(Player player, Location location, String enchantType) {
        // Xử lý âm thanh sử dụng SoundManager tương thích đa phiên bản
        String soundConfig = File.getEnchants().getString("axe_enchant." + enchantType + ".sound", "ENTITY_EXPERIENCE_ORB_PICKUP:0.3:1.2");
        if (soundConfig != null && !soundConfig.isEmpty()) {
            try {
                String[] soundParts = soundConfig.split(":");
                String soundName = soundParts[0];
                float volume = soundParts.length > 1 ? Float.parseFloat(soundParts[1]) : 0.3f;
                float pitch = soundParts.length > 2 ? Float.parseFloat(soundParts[2]) : 1.2f;

                // Sử dụng SoundManager để phát âm thanh tương thích đa phiên bản
                SoundManager.playSound(player, soundName, volume, pitch);
            } catch (Exception e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Lỗi khi phát âm thanh " + enchantType + ": " + e.getMessage());
                }
            }
        }
        
        // Xử lý hiệu ứng hạt sử dụng ParticleEffect tương thích đa phiên bản
        String particleConfig = File.getEnchants().getString("axe_enchant." + enchantType + ".particle", "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:5");
        if (particleConfig != null && !particleConfig.isEmpty()) {
            try {
                String[] particleParts = particleConfig.split(":");
                String particleName = particleParts[0];
                double offsetX = particleParts.length > 1 ? Double.parseDouble(particleParts[1]) : 0.3;
                double offsetY = particleParts.length > 2 ? Double.parseDouble(particleParts[2]) : 0.3;
                double offsetZ = particleParts.length > 3 ? Double.parseDouble(particleParts[3]) : 0.3;
                double speed = particleParts.length > 4 ? Double.parseDouble(particleParts[4]) : 0.05;
                int count = particleParts.length > 5 ? Integer.parseInt(particleParts[5]) : 5;

                // Sử dụng ParticleEffect để tạo hiệu ứng tương thích đa phiên bản
                ParticleEffect.spawnParticle(location, particleName, count, offsetX, offsetY, offsetZ, speed);
            } catch (Exception e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Lỗi khi xử lý cấu hình hiệu ứng hạt " + enchantType + ": " + e.getMessage());
                }
            }
        }
    }
    
    // Các method isParticleEnumSupported() và convertParticleName() đã được thay thế
    // bằng ParticleEffect compatibility class
    
    /**
     * Gửi thông báo hiệu ứng khi sử dụng phù phép
     * 
     * @param player Người chơi nhận thông báo
     * @param level Cấp độ phù phép
     * @param blockCount Số lượng block đã phá
     * @param enchantType Loại phù phép
     */
    private void sendEffectMessage(Player player, int level, int blockCount, String enchantType) {
        if (!File.getEnchants().getBoolean("axe_enchant." + enchantType + ".effect_message.enabled", true)) {
            return;
        }

        String format = File.getEnchants().getString("axe_enchant." + enchantType + ".effect_message.format",
                                                 "&a✓ &ePhù phép %name% %level% &aphá &f%blocks% &ablocks!");
        
        if (format != null && !format.isEmpty()) {
            String enchantName = "";
            switch (enchantType) {
                case AxeEnchantManager.TREE_CUTTER: enchantName = "Chặt Cây"; break;
                case AxeEnchantManager.LEAF_COLLECTOR: enchantName = "Thu Lá"; break;
                case AxeEnchantManager.REGROWTH: enchantName = "Tái Sinh"; break;
                case AxeEnchantManager.AUTO_PLANT: enchantName = "Tự Trồng"; break;
            }
            
            String message = format.replace("%level%", getRomanLevel(level))
                                 .replace("%blocks%", String.valueOf(blockCount))
                                 .replace("%name%", enchantName);
            
            player.sendMessage(Chat.colorizewp(message));
        }
    }
    
    /**
     * Chuyển đổi cấp độ số thành chữ số La Mã
     */
    private String getRomanLevel(int level) {
        switch (level) {
            case 1: return "I";
            case 2: return "II";
            case 3: return "III";
            default: return String.valueOf(level);
        }
    }
    
    /**
     * Xử lý việc phá nhiều khối cùng một lúc
     * @param player Người chơi
     * @param blocks Danh sách các block cần phá
     */
    private void handleBlockBreak(Player player, List<Block> blocks) {
        boolean debug = Storage.getStorage().isDebug();

        // Tách riêng lá cây và các block khác
        List<Block> leafBlocks = new ArrayList<>();
        List<Block> woodBlocks = new ArrayList<>();
        List<Block> otherBlocks = new ArrayList<>();

        for (Block block : blocks) {
            String blockType = block.getType().name().toUpperCase();
            if (blockType.contains("LEAVES") || blockType.equals("LEAVES") || blockType.equals("LEAVES_2")) {
                leafBlocks.add(block);
            } else if (blockType.contains("LOG") || blockType.contains("WOOD") ||
                       blockType.endsWith("_STEM") || blockType.contains("STRIPPED")) {
                woodBlocks.add(block);
            } else {
                otherBlocks.add(block);
            }
        }

        // Xử lý lá cây - luôn drop vanilla, không sử dụng hệ thống kho
        if (!leafBlocks.isEmpty()) {
            if (debug) Storage.getStorage().getLogger().info("Axe Enchant: Xử lý " + leafBlocks.size() + " lá cây bằng cách drop vanilla");
            handleLeafBlocksBreak(player, leafBlocks);
        }

        // Xử lý gỗ - sử dụng breakNaturally
        if (!woodBlocks.isEmpty()) {
            if (debug) Storage.getStorage().getLogger().info("Axe Enchant: Xử lý " + woodBlocks.size() + " block gỗ bằng breakNaturally");
            for (Block block : woodBlocks) {
                block.breakNaturally(player.getInventory().getItemInMainHand());
            }
        }

        // Xử lý các block khác theo hệ thống kho nếu có auto pickup
        if (!otherBlocks.isEmpty()) {
            if (MineManager.isAutoPickup(player)) {
                if (debug) Storage.getStorage().getLogger().info("Axe Enchant: Xử lý " + otherBlocks.size() + " block khác với auto pickup");

                Map<String, Integer> blockResults = MineManager.processBlocksBatchFromList(player, otherBlocks);

                // Phá hủy các block đã xử lý (không drop items)
                for (Block block : otherBlocks) {
                    block.setType(Material.AIR);
                }

                // Thông báo kết quả
                if (!blockResults.isEmpty() && File.getEnchants().getBoolean("axe_enchant.show_batch_message", true)) {
                    StringBuilder message = new StringBuilder(Objects.requireNonNull(
                        File.getEnchants().getString("axe_enchant.batch_message", "&aBạn đã thu hoạch được:"))
                    );

                    for (Map.Entry<String, Integer> entry : blockResults.entrySet()) {
                        String materialName = MineManager.getMaterialDisplayName(entry.getKey());
                        message.append("\n&7- &e").append(materialName).append(": &a+").append(entry.getValue());
                    }

                    player.sendMessage(Chat.colorizewp(message.toString()));
                }
            } else {
                if (debug) Storage.getStorage().getLogger().info("Axe Enchant: Xử lý " + otherBlocks.size() + " block khác thông thường");

                // Cho phép các block bình thường rơi ra nếu không bật auto pickup
                for (Block block : otherBlocks) {
                    block.breakNaturally(player.getInventory().getItemInMainHand());
                }
            }
        }
    }

    /**
     * Xử lý phá lá cây với drop vanilla (không sử dụng hệ thống kho)
     * @param player Người chơi
     * @param leafBlocks Danh sách các block lá cây cần phá
     */
    private void handleLeafBlocksBreak(Player player, List<Block> leafBlocks) {
        boolean debug = Storage.getStorage().isDebug();

        for (Block block : leafBlocks) {
            Location dropLocation = block.getLocation().add(0.5, 0.5, 0.5);
            Material leafType = block.getType();

            if (debug) {
                Storage.getStorage().getLogger().info("Xử lý lá cây " + leafType + " tại " + block.getLocation());
            }

            // Xóa tất cả metadata có thể can thiệp vào drop
            if (block.hasMetadata("NoDrops")) {
                block.removeMetadata("NoDrops", Storage.getStorage());
            }

            // Phá block trước
            block.setType(Material.AIR);

            // Tạo drop lá cây trực tiếp - đảm bảo 100% drop
            ItemStack leafDrop = new ItemStack(leafType, 1);
            dropLocation.getWorld().dropItemNaturally(dropLocation, leafDrop);

            if (debug) {
                Storage.getStorage().getLogger().info("Đã drop " + leafType + " tại " + dropLocation);
            }

            // Có thể thêm drop sapling với tỷ lệ thấp (giống vanilla)
            if (Math.random() < 0.05) { // 5% chance như vanilla
                Material saplingType = getSaplingFromLeaf(leafType);
                if (saplingType != null) {
                    ItemStack saplingDrop = new ItemStack(saplingType, 1);
                    dropLocation.getWorld().dropItemNaturally(dropLocation, saplingDrop);

                    if (debug) {
                        Storage.getStorage().getLogger().info("Đã drop sapling " + saplingType + " tại " + dropLocation);
                    }
                }
            }
        }
    }

    /**
     * Lấy loại sapling tương ứng với lá cây
     * @param leafType Loại lá cây
     * @return Loại sapling tương ứng hoặc null nếu không tìm thấy
     */
    private Material getSaplingFromLeaf(Material leafType) {
        String leafName = leafType.name();

        // Xử lý cho các phiên bản Minecraft khác nhau
        if (leafName.equals("LEAVES")) {
            return getMaterialSafely("OAK_SAPLING", "SAPLING");
        } else if (leafName.equals("LEAVES_2")) {
            return getMaterialSafely("ACACIA_SAPLING", "SAPLING");
        } else if (leafName.endsWith("_LEAVES")) {
            String saplingName = leafName.replace("_LEAVES", "_SAPLING");
            return getMaterialSafely(saplingName, "SAPLING");
        }

        return null;
    }

    /**
     * Lấy Material một cách an toàn cho tương thích đa phiên bản
     * @param primaryName Tên chính (phiên bản mới)
     * @param fallbackName Tên dự phòng (phiên bản cũ)
     * @return Material hoặc null nếu không tìm thấy
     */
    private Material getMaterialSafely(String primaryName, String fallbackName) {
        // Sử dụng MaterialCompatibility để xử lý an toàn
        Material material = com.hongminh54.storage.Utils.MaterialCompatibility.getMaterialSafely(primaryName);
        if (material != null) {
            return material;
        }

        // Thử với fallback name
        return com.hongminh54.storage.Utils.MaterialCompatibility.getMaterialSafely(fallbackName);
    }
    
    /**
     * Kiểm tra block có phải do người chơi đặt không
     * 
     * @param block Block cần kiểm tra
     * @return true nếu block do người chơi đặt
     */
    private boolean isPlacedBlock(Block block) {
        List<MetadataValue> metaDataValues = block.getMetadata("PlacedBlock");
        for (MetadataValue value : metaDataValues) {
            return value.asBoolean();
        }
        return false;
    }
} 