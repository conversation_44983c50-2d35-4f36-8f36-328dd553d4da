package com.hongminh54.storage.Manager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.logging.Level;

import com.hongminh54.storage.Listeners.SpecialMaterialListener;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import com.hongminh54.storage.Events.MiningEvent;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.ParticleEffect;

/**
 * Quản lý tài nguyên đặc biệt
 * Lớp này xử lý việc tạo ra và quản lý tài nguyên đặc biệt khi người chơi đào khối.
 */
public class SpecialMaterialManager {
    
    private static final Random random = new Random();
    private static boolean enabled = false;
    private static double defaultChance = 5.0;
    private static final Map<String, SpecialMaterial> specialMaterials = new HashMap<>();
    private static final Map<String, List<SpecialMaterial>> allTypeMaterials = new HashMap<>();
    private static final Map<MiningEvent.EventType, List<SpecialMaterial>> eventOnlyMaterials = new HashMap<>();
    private static FileConfiguration config;
    
    // Hệ số nhân tỷ lệ rơi khoáng sản đặc biệt cho từng loại sự kiện
    private static final Map<MiningEvent.EventType, Double> eventMultipliers = new HashMap<>();
    // Trạng thái bật/tắt của hệ thống event multipliers
    private static boolean eventMultipliersEnabled = true;
    
    // Cấu hình hiệu ứng mặc định
    private static String defaultSound = "ENTITY_PLAYER_LEVELUP:0.7:1.2";
    private static String defaultParticle = "SPELL_WITCH:0.3:0.5:0.3:0.05:15";
    private static boolean showMessage = true;
    private static String messageFormat = "&a&l✓ &aBạn đã tìm thấy &f#item_name# &ađặc biệt!";
    
    // Cấu hình thông báo sự kiện
    private static Map<String, String> notificationMessages = new HashMap<>();
    
    /**
     * Khởi tạo người quản lý khoáng sản đặc biệt và tải cấu hình
     */
    public static void initialize() {
        loadConfig();
        initializeEventMultipliers();
    }
    
    /**
     * Tải cấu hình khoáng sản đặc biệt từ tệp YAML
     */
    public static void loadConfig() {
        specialMaterials.clear();
        allTypeMaterials.clear();
        eventOnlyMaterials.clear();
        notificationMessages.clear();

        try {
            // Sử dụng File.getSpecialMaterials() thay vì tải trực tiếp file
            config = File.getSpecialMaterials();
            
            if (config == null) {
                Storage.getStorage().getLogger().severe("Không thể tải file special_material.yml");
                enabled = false;
                return;
            }
            
            // Tải các cài đặt cơ bản
            enabled = config.getBoolean("enabled", true);
            defaultChance = config.getDouble("chance", 5.0);
            
            // Tải cấu hình hiệu ứng mặc định
            ConfigurationSection effectsSection = config.getConfigurationSection("effects");
            if (effectsSection != null) {
                defaultSound = effectsSection.getString("sound", defaultSound);
                defaultParticle = effectsSection.getString("particle", defaultParticle);
                
                // Không đọc giá trị show_message từ effects nữa
                // Tin nhắn sẽ hoàn toàn được điều khiển bởi notifications.obtain.show_message
                
                // Không đọc messageFormat từ effects nữa
                // messageFormat = effectsSection.getString("message", messageFormat);
            }
            
            // Tải toàn bộ cấu hình thông báo nếu có
            loadNotificationMessages();
            
            // Tải danh sách khoáng sản đặc biệt
            ConfigurationSection materialsSection = config.getConfigurationSection("special_materials");
            if (materialsSection != null) {
                for (String key : materialsSection.getKeys(false)) {
                    ConfigurationSection materialSection = materialsSection.getConfigurationSection(key);
                    if (materialSection != null) {
                        String fromBlock = materialSection.getString("from_block", "");
                        String displayName = materialSection.getString("display_name", key);
                        List<String> lore = materialSection.getStringList("lore");
                        String materialStr = materialSection.getString("material", "STONE");
                        byte data = (byte) materialSection.getInt("data", 0);
                        boolean glow = materialSection.getBoolean("glow", true);
                        double chance = materialSection.getDouble("chance", defaultChance);
                        
                        // Đọc custom_model_data nếu có
                        int customModelData = materialSection.getInt("custom_model_data", 0);
                        
                        // Kiểm tra xem khoáng sản có chỉ xuất hiện trong sự kiện cụ thể không
                        String eventOnly = materialSection.getString("event_only", "");
                        MiningEvent.EventType eventType = null;
                        
                        if (!eventOnly.isEmpty()) {
                            try {
                                eventType = MiningEvent.EventType.valueOf(eventOnly);
                            } catch (IllegalArgumentException e) {
                                Storage.getStorage().getLogger().warning("Loại sự kiện không hợp lệ cho khoáng sản đặc biệt '" + key + "': " + eventOnly);
                            }
                        }
                        
                        // Tải cấu hình hiệu ứng riêng nếu có
                        String effectSound = defaultSound;
                        String effectParticle = defaultParticle;
                        
                        if (materialSection.isConfigurationSection("effects")) {
                            ConfigurationSection materialEffectsSection = materialSection.getConfigurationSection("effects");
                            if (materialEffectsSection.isSet("sound")) {
                                effectSound = materialEffectsSection.getString("sound", defaultSound);
                            }
                            if (materialEffectsSection.isSet("particle")) {
                                effectParticle = materialEffectsSection.getString("particle", defaultParticle);
                            }
                        }
                        
                        // Tạo đối tượng khoáng sản đặc biệt
                        SpecialMaterial specialMaterial = new SpecialMaterial(key, fromBlock, displayName, lore,
                                materialStr, data, glow, chance, effectSound, effectParticle, eventType, customModelData);

                        // Nếu khoáng sản chỉ xuất hiện trong sự kiện cụ thể, thêm vào danh sách tương ứng
                        if (eventType != null) {
                            if (!eventOnlyMaterials.containsKey(eventType)) {
                                eventOnlyMaterials.put(eventType, new ArrayList<>());
                            }
                            eventOnlyMaterials.get(eventType).add(specialMaterial);
                        }
                        
                        // Nếu là loại ALL, thêm vào danh sách dành cho tất cả khoáng sản
                        if ("ALL".equalsIgnoreCase(fromBlock)) {
                            for (String blockType : MineManager.getPluginBlocks()) {
                                if (!allTypeMaterials.containsKey(blockType)) {
                                    allTypeMaterials.put(blockType, new ArrayList<>());
                                }
                                allTypeMaterials.get(blockType).add(specialMaterial);
                            }
                        } else {
                            // Thêm vào danh sách theo loại khoáng sản
                            // Lấy các khối đồng nhất với fromBlock được đặt trong cấu hình
                            boolean foundMatchingBlock = false;

                            // Duyệt qua tất cả các block trong config.yml
                            for (String configuredBlock : File.getConfig().getConfigurationSection("blocks").getKeys(false)) {
                                // Lấy phần material của block (trước dấu ";")
                                String blockMaterial = configuredBlock.split(";")[0];

                                // Nếu material của block trùng với fromBlock
                                if (blockMaterial.equalsIgnoreCase(fromBlock)) {
                                    // Lấy loại drop của block này từ config
                                    String dropMaterial = File.getConfig().getString("blocks." + configuredBlock + ".drop");

                                    if (dropMaterial != null) {
                                        // Kiểm tra xem khoáng sản đặc biệt này đã được ánh xạ cho dropMaterial chưa
                                        boolean alreadyMapped = specialMaterials.containsKey(dropMaterial) &&
                                                               specialMaterials.get(dropMaterial).getId().equals(key);
                                        
                                        // Nếu đã được ánh xạ, bỏ qua để tránh trùng lặp
                                        if (alreadyMapped) {
                                            if (Storage.getStorage().isDebug()) {
                                                Storage.getStorage().getLogger().info("Khoáng sản đặc biệt '" + key + 
                                                    "' đã được ánh xạ đến drop '" + dropMaterial + "', bỏ qua để tránh trùng lặp");
                                            }
                                            continue;
                                        }
                                        
                                        // Thêm vào danh sách allTypeMaterials với dropMaterial là key
                                        if (!allTypeMaterials.containsKey(dropMaterial)) {
                                            allTypeMaterials.put(dropMaterial, new ArrayList<>());
                                        }
                                        allTypeMaterials.get(dropMaterial).add(specialMaterial);

                                        // Cũng lưu vào specialMaterials cho tương thích ngược
                                        specialMaterials.put(dropMaterial, specialMaterial);

                                        foundMatchingBlock = true;

                                        if (Storage.getStorage().isDebug()) {
                                            Storage.getStorage().getLogger().info("Đã ánh xạ khoáng sản đặc biệt '" +
                                                key + "' từ block '" + fromBlock + "' đến drop '" + dropMaterial + "'");
                                        }
                                    }
                                }
                            }
                            
                            if (!foundMatchingBlock) {
                                // Nếu không tìm thấy khối khớp, log cảnh báo
                                Storage.getStorage().getLogger().warning("Không tìm thấy khối nào phù hợp với fromBlock '" + fromBlock + 
                                        "' cho khoáng sản đặc biệt '" + key + "'. Kiểm tra lại cấu hình của bạn.");
                                
                                // Tạo key tạm thời cho block không tìm thấy để ít nhất có thể hiển thị trong danh sách
                                String tempKey = fromBlock + ";0";
                                if (!allTypeMaterials.containsKey(tempKey)) {
                                    allTypeMaterials.put(tempKey, new ArrayList<>());
                                }
                                allTypeMaterials.get(tempKey).add(specialMaterial);
                                specialMaterials.put(tempKey, specialMaterial);
                            }
                        }
                    }
                }
            }

            Storage.getStorage().getLogger().info("Đã tải " + specialMaterials.size() + " tài nguyên đặc biệt và "
                    + (allTypeMaterials.size() - specialMaterials.size()) + " tài nguyên toàn phần (ALL) từ cấu hình.");

            // Thêm thông tin về khoáng sản chỉ xuất hiện trong sự kiện
            if (!eventOnlyMaterials.isEmpty()) {
                int totalEventMaterials = 0;
                for (List<SpecialMaterial> materials : eventOnlyMaterials.values()) {
                    totalEventMaterials += materials.size();
                }
                Storage.getStorage().getLogger().info("Đã tải " + totalEventMaterials + " tài nguyên đặc biệt chỉ xuất hiện trong sự kiện.");
            }

            // Log thông tin chi tiết về tài nguyên đặc biệt
            if (Storage.getStorage().isDebug()) {
                logSpecialMaterialDetails();
            }
            
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi tải cấu hình khoáng sản đặc biệt: " + e.getMessage(), e);
            enabled = false;
        }
    }
    
    /**
     * Ghi log thông tin chi tiết về khoáng sản đặc biệt
     * Giúp debug các vấn đề liên quan đến from_block
     */
    private static void logSpecialMaterialDetails() {
        Storage.getStorage().getLogger().info("=== CHI TIẾT TÀI NGUYÊN ĐẶC BIỆT ===");

        // Log các loại tài nguyên đặc biệt đã đăng ký
        Storage.getStorage().getLogger().info("=== SPECIAL MATERIALS (" + specialMaterials.size() + ") ===");
        for (Map.Entry<String, SpecialMaterial> entry : specialMaterials.entrySet()) {
            String blockKey = entry.getKey();
            SpecialMaterial material = entry.getValue();
            Storage.getStorage().getLogger().info(
                String.format("Block '%s' -> Material '%s' (from_block: '%s', chance: %.2f%%)",
                    blockKey, material.getId(), material.getFromBlock(), material.getChance())
            );
        }

        // Log các loại block và tài nguyên đặc biệt tương ứng
        Storage.getStorage().getLogger().info("=== ALL TYPE MATERIALS (" + allTypeMaterials.size() + ") ===");
        for (Map.Entry<String, List<SpecialMaterial>> entry : allTypeMaterials.entrySet()) {
            String blockKey = entry.getKey();
            List<SpecialMaterial> materials = entry.getValue();

            StringBuilder materialNames = new StringBuilder();
            for (SpecialMaterial material : materials) {
                if (materialNames.length() > 0) materialNames.append(", ");
                materialNames.append(material.getId());
            }
            
            Storage.getStorage().getLogger().info(
                String.format("Block '%s' -> Materials: %s", blockKey, materialNames.toString())
            );
        }

        // Log các tài nguyên chỉ xuất hiện trong sự kiện
        Storage.getStorage().getLogger().info("=== EVENT ONLY MATERIALS ===");
        for (Map.Entry<MiningEvent.EventType, List<SpecialMaterial>> entry : eventOnlyMaterials.entrySet()) {
            MiningEvent.EventType eventType = entry.getKey();
            List<SpecialMaterial> materials = entry.getValue();

            StringBuilder materialNames = new StringBuilder();
            for (SpecialMaterial material : materials) {
                if (materialNames.length() > 0) materialNames.append(", ");
                materialNames.append(material.getId());
            }
            
            Storage.getStorage().getLogger().info(
                String.format("Event '%s' -> Materials: %s", eventType.name(), materialNames.toString())
            );
        }
        
        Storage.getStorage().getLogger().info("=== KẾT THÚC CHI TIẾT KHOÁNG SẢN ĐẶC BIỆT ===");
    }
    
    /**
     * Tải toàn bộ cấu hình thông báo từ file
     */
    private static void loadNotificationMessages() {
        if (config == null) return;
        
        // Tải thông báo từ phần notifications.obtain.player nếu có
        if (config.isString("notifications.obtain.player")) {
            String obtainPlayerMessage = config.getString("notifications.obtain.player", "&a&l✓ &aBạn đã tìm thấy &f#item_name# &ađặc biệt!");
            notificationMessages.put("obtain.player", obtainPlayerMessage);
            // Không cần ghi đè biến messageFormat nữa vì chúng ta sẽ luôn đọc từ notificationMessages
        } else {
            // Đảm bảo có giá trị mặc định nếu không có trong config
            notificationMessages.put("obtain.player", "&a&l✓ &aBạn đã tìm thấy &f#item_name# &ađặc biệt!");
        }
        
        // Không lưu giá trị từ cấu hình effects.show_message nữa vì phần effects chỉ xử lý hiệu ứng
        
        // Kiểm tra xem có cấu hình show_message trong phần notifications.obtain không
        if (config.isBoolean("notifications.obtain.show_message")) {
            boolean notificationShowMessage = config.getBoolean("notifications.obtain.show_message", true);
            notificationMessages.put("obtain.show_message", String.valueOf(notificationShowMessage));
            
            // Log để debug
            Storage.getStorage().getLogger().info("Đã đọc cấu hình notifications.obtain.show_message: " + notificationShowMessage);
        } else {
            // Nếu không có cấu hình trong notifications.obtain.show_message, mặc định là true
            notificationMessages.put("obtain.show_message", "true");
            Storage.getStorage().getLogger().info("Không tìm thấy cấu hình notifications.obtain.show_message, đặt giá trị mặc định là true");
        }
        
        // Tải các thông báo sự kiện
        ConfigurationSection eventNotificationSection = config.getConfigurationSection("notifications.event");
        if (eventNotificationSection != null) {
            for (String key : eventNotificationSection.getKeys(false)) {
                if (eventNotificationSection.isString(key)) {
                    notificationMessages.put("event." + key, eventNotificationSection.getString(key));
                } else if (eventNotificationSection.isList(key)) {
                    List<String> messages = eventNotificationSection.getStringList(key);
                    for (int i = 0; i < messages.size(); i++) {
                        notificationMessages.put("event." + key + "." + i, messages.get(i));
                    }
                } else if (eventNotificationSection.isConfigurationSection(key)) {
                    ConfigurationSection subSection = eventNotificationSection.getConfigurationSection(key);
                    for (String subKey : subSection.getKeys(false)) {
                        if (subSection.isString(subKey)) {
                            notificationMessages.put("event." + key + "." + subKey, subSection.getString(subKey));
                        } else if (subSection.isList(subKey)) {
                            List<String> subMessages = subSection.getStringList(subKey);
                            for (int i = 0; i < subMessages.size(); i++) {
                                notificationMessages.put("event." + key + "." + subKey + "." + i, subMessages.get(i));
                            }
                        }
                    }
                }
            }
        }
        
        // Tải các thông báo nhận được khoáng sản
        ConfigurationSection obtainSection = config.getConfigurationSection("notifications.obtain");
        if (obtainSection != null) {
            for (String key : obtainSection.getKeys(false)) {
                if (obtainSection.isString(key)) {
                    notificationMessages.put("obtain." + key, obtainSection.getString(key));
                }
            }
        }
        
        // Tải các thông báo Ngọc Quý Cuối Tuần
        ConfigurationSection gemSection = config.getConfigurationSection("notifications.weekend_gem");
        if (gemSection != null) {
            for (String key : gemSection.getKeys(false)) {
                if (gemSection.isString(key)) {
                    notificationMessages.put("weekend_gem." + key, gemSection.getString(key));
                }
            }
        }
        
        Storage.getStorage().getLogger().info("Đã tải " + notificationMessages.size() + " thông báo từ cấu hình khoáng sản đặc biệt.");
    }
    
    /**
     * Lấy tin nhắn từ cấu hình thông báo
     * @param key Khóa của tin nhắn
     * @param defaultMsg Tin nhắn mặc định nếu không tìm thấy
     * @return Tin nhắn đã được cấu hình
     */
    public static String getMessage(String key, String defaultMsg) {
        return notificationMessages.getOrDefault(key, defaultMsg);
    }
    
    /**
     * Khởi tạo các hệ số nhân cho từng loại sự kiện
     */
    private static void initializeEventMultipliers() {
        // Đặt giá trị mặc định
        eventMultipliers.clear();
        eventMultipliers.put(MiningEvent.EventType.NONE, 1.0);
        eventMultipliers.put(MiningEvent.EventType.DOUBLE_DROP, 2.0);
        eventMultipliers.put(MiningEvent.EventType.FORTUNE_BOOST, 1.5);
        eventMultipliers.put(MiningEvent.EventType.RARE_MATERIALS, 3.0);
        eventMultipliers.put(MiningEvent.EventType.COMMUNITY_GOAL, 1.25);

        // Tải từ cấu hình nếu có
        if (config != null) {
            // Đọc trạng thái bật/tắt event multipliers
            eventMultipliersEnabled = config.getBoolean("event_multipliers_enabled", true);

            if (config.isConfigurationSection("event_multipliers")) {
                ConfigurationSection eventSection = config.getConfigurationSection("event_multipliers");
                for (String key : eventSection.getKeys(false)) {
                    try {
                        MiningEvent.EventType eventType = MiningEvent.EventType.valueOf(key);
                        double multiplier = eventSection.getDouble(key, 1.0);
                        eventMultipliers.put(eventType, multiplier);
                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().info("Đã tải hệ số nhân sự kiện " + key + ": " + multiplier);
                        }
                    } catch (IllegalArgumentException e) {
                        Storage.getStorage().getLogger().warning("Loại sự kiện không hợp lệ trong cấu hình khoáng sản đặc biệt: " + key);
                    }
                }
            } else {
                Storage.getStorage().getLogger().info("Không tìm thấy phần cấu hình event_multipliers, sử dụng giá trị mặc định");
            }

            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Trạng thái Event Multipliers: " + (eventMultipliersEnabled ? "Bật" : "Tắt"));
            }
        }
    }
    
    /**
     * Kiểm tra xem tính năng khoáng sản đặc biệt có được bật không
     * @return true nếu được bật
     */
    public static boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Kiểm tra xem block có phải là tài nguyên đặc biệt không
     * @param block Block cần kiểm tra
     * @return SpecialMaterial nếu block là tài nguyên đặc biệt, null nếu không phải
     */
    public static SpecialMaterial getSpecialMaterial(Block block) {
        if (!enabled) return null;

        String blockType = MineManager.getDrop(block);
        return specialMaterials.get(blockType);
    }
    
    /**
     * Thử tạo ra tài nguyên đặc biệt dựa trên tỉ lệ
     * @param player Người chơi đào tài nguyên
     * @param block Block được đào
     * @return SpecialMaterialResult tài nguyên đặc biệt nếu thành công, null nếu không
     */
    public static SpecialMaterialResult tryCreateSpecialMaterial(Player player, Block block) {
        if (!enabled) return null;
        
        String blockType = MineManager.getDrop(block);
        
        // Nếu blockType không hợp lệ, trả về null
        if (blockType == null || blockType.isEmpty() || "UNKNOWN_DROP".equals(blockType)) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Không tìm thấy blockType hợp lệ cho block: " + block.getType().name());
            }
            return null;
        }
        
        // Debug log để theo dõi blockType
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Đang kiểm tra khoáng sản đặc biệt cho block: " + block.getType().name() + ", blockType (drop): " + blockType);
            Storage.getStorage().getLogger().info("Danh sách allTypeMaterials keys: " + String.join(", ", allTypeMaterials.keySet()));
        }

        // Lấy sự kiện hiện tại nếu có
        MiningEvent eventManager = MiningEvent.getInstance();
        MiningEvent.EventType currentEventType = (eventManager != null && eventManager.isActive())
                ? eventManager.getCurrentEventType() : MiningEvent.EventType.NONE;

        // Lấy hệ số nhân từ sự kiện hiện tại
        double eventMultiplier = getEventMultiplier();

        // Kiểm tra xem có khoáng sản đặc biệt nào khớp với loại block này không
        List<SpecialMaterial> possibleMaterials = allTypeMaterials.get(blockType);
        
        // Debug log để theo dõi danh sách material
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Kiểm tra khoáng sản đặc biệt cho blockType (drop): " + blockType);
            Storage.getStorage().getLogger().info("possibleMaterials cho " + blockType + ": " + (possibleMaterials != null ? possibleMaterials.size() : "null"));

            // In ra các block có trong allTypeMaterials mà giống blockType
            if (possibleMaterials == null || possibleMaterials.isEmpty()) {
                Storage.getStorage().getLogger().info("Đang kiểm tra khớp một phần (thủ công)...");

                // Tìm kiếm thủ công trong allTypeMaterials
                String blockMaterial = blockType.split(";")[0]; // Lấy phần material (trước dấu ";")
                for (Map.Entry<String, List<SpecialMaterial>> entry : allTypeMaterials.entrySet()) {
                    String key = entry.getKey();
                    String keyMaterial = key.split(";")[0]; // Lấy phần material của key

                    if (keyMaterial.equalsIgnoreCase(blockMaterial)) {
                        Storage.getStorage().getLogger().info("Tìm thấy khớp một phần: " + key + " ~ " + blockType);
                    }
                }
            }
        }

        if (possibleMaterials == null || possibleMaterials.isEmpty()) return null;
        
        // Ưu tiên kiểm tra khoáng sản sự kiện trước
        if (currentEventType != MiningEvent.EventType.NONE && eventOnlyMaterials.containsKey(currentEventType)) {
            List<SpecialMaterial> eventMaterials = eventOnlyMaterials.get(currentEventType);
            if (!eventMaterials.isEmpty()) {
                for (SpecialMaterial specialMaterial : eventMaterials) {
                    // Kiểm tra xem khoáng sản sự kiện có phù hợp với blockType không
                    if (!"ALL".equalsIgnoreCase(specialMaterial.getFromBlock()) &&
                        !blockType.split(";")[0].equalsIgnoreCase(specialMaterial.getFromBlock())) {
                        continue; // Bỏ qua nếu khoáng sản không áp dụng cho loại block này
                    }

                    // Tính tỉ lệ đã điều chỉnh
                    double adjustedChance = specialMaterial.getChance() * eventMultiplier;
                    
                    // Giới hạn tỉ lệ tối đa 100%
                    if (adjustedChance > 100.0) {
                        adjustedChance = 100.0;
                    }
                    
                    // Tỉ lệ ngẫu nhiên từ 0 đến 100 (không phải từ 0 đến 1)
                    double roll = random.nextDouble() * 100.0;
                    
                    // Log cho mục đích debug
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info(
                            String.format("Tỉ lệ khoáng sản sự kiện cho %s: %.2f%% (cơ bản: %.2f%%, hệ số sự kiện: %.2fx), giá trị ngẫu nhiên: %.2f",
                                specialMaterial.getId(), adjustedChance, specialMaterial.getChance(), eventMultiplier, roll)
                        );
                    }

                    // Đảm bảo khoáng sản luôn rơi nếu tỉ lệ là 100% hoặc roll <= adjustedChance
                    if (adjustedChance >= 100.0 || roll <= adjustedChance) {
                        // Tạo item khoáng sản đặc biệt
                        return new SpecialMaterialResult(specialMaterial, specialMaterial.createItemStack());
                    }
                }
            }
        }

        // Sau đó thử các khoáng sản thông thường
        for (SpecialMaterial specialMaterial : possibleMaterials) {
            // Kiểm tra xem khoáng sản có chỉ xuất hiện trong sự kiện cụ thể không
            if (specialMaterial.getEventOnly() != null && specialMaterial.getEventOnly() != currentEventType) {
                continue; // Bỏ qua nếu khoáng sản chỉ xuất hiện trong sự kiện khác
            }

            // Tính tỉ lệ đã điều chỉnh
            double adjustedChance = specialMaterial.getChance() * eventMultiplier;

            // Giới hạn tỉ lệ tối đa 100%
            if (adjustedChance > 100.0) {
                adjustedChance = 100.0;
            }

            // Tỉ lệ ngẫu nhiên từ 0 đến 100 (không phải từ 0 đến 1)
            double roll = random.nextDouble() * 100.0;

            // Log cho mục đích debug
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info(
                    String.format("Tỉ lệ khoáng sản đặc biệt cho %s: %.2f%% (cơ bản: %.2f%%, hệ số sự kiện: %.2fx), giá trị ngẫu nhiên: %.2f",
                        specialMaterial.getId(), adjustedChance, specialMaterial.getChance(), eventMultiplier, roll)
                );
            }

            // Đảm bảo khoáng sản luôn rơi nếu tỉ lệ đủ cao
            if (adjustedChance >= 100.0 || roll <= adjustedChance) {
                // Tạo item khoáng sản đặc biệt
                return new SpecialMaterialResult(specialMaterial, specialMaterial.createItemStack());
            }
        }
        
        return null;
    }
    
    /**
     * Lấy hệ số nhân tỷ lệ rơi khoáng sản đặc biệt dựa trên sự kiện hiện tại
     * @return Hệ số nhân tỷ lệ rơi
     */
    public static double getEventMultiplier() {
        // Kiểm tra xem event multipliers có được bật không
        if (!eventMultipliersEnabled) {
            return 1.0;
        }

        MiningEvent eventManager = MiningEvent.getInstance();
        if (eventManager != null && eventManager.isActive()) {
            MiningEvent.EventType currentEventType = eventManager.getCurrentEventType();
            double multiplier = eventMultipliers.getOrDefault(currentEventType, 1.0);

            // Log cho debug
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Hệ số nhân sự kiện " + currentEventType + ": " + multiplier);
            }

            return multiplier;
        }
        return 1.0;
    }
    
    /**
     * Lấy thông tin về hệ số nhân tỷ lệ rơi khoáng sản đặc biệt hiện tại
     * @return Thông tin hệ số nhân tỷ lệ rơi
     */
    public static String getEventMultiplierInfo() {
        if (!eventMultipliersEnabled) {
            return Chat.colorize("&7Hệ thống Event Multipliers đã bị tắt");
        }

        double multiplier = getEventMultiplier();
        if (multiplier > 1.0) {
            return Chat.colorize("&a+" + String.format("%.1f", (multiplier - 1.0) * 100) + "% tỷ lệ khoáng sản đặc biệt");
        } else {
            return Chat.colorize("&cKhông có tăng tỷ lệ khoáng sản đặc biệt");
        }
    }

    /**
     * Kiểm tra xem hệ thống event multipliers có được bật không
     * @return true nếu được bật
     */
    public static boolean isEventMultipliersEnabled() {
        return eventMultipliersEnabled;
    }

    /**
     * Bật/tắt hệ thống event multipliers
     * @param enabled true để bật, false để tắt
     */
    public static void setEventMultipliersEnabled(boolean enabled) {
        eventMultipliersEnabled = enabled;
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Event Multipliers đã được " + (enabled ? "bật" : "tắt"));
        }
    }
    
    /**
     * Kiểm tra xem người chơi có chỗ trống trong túi để nhận vật phẩm không
     * @param player Người chơi cần kiểm tra
     * @return true nếu có chỗ trống
     */
    public static boolean hasInventorySpace(Player player) {
        return player.getInventory().firstEmpty() != -1;
    }
    
    /**
     * Phát hiệu ứng khi người chơi nhận được tài nguyên đặc biệt
     * @param player Người chơi nhận được tài nguyên đặc biệt
     * @param specialMaterial Tài nguyên đặc biệt
     * @param itemStack ItemStack của tài nguyên đặc biệt
     */
    public static void playEffects(Player player, SpecialMaterial specialMaterial, ItemStack itemStack) {
        try {
            // Phát hiệu ứng âm thanh
            String soundConfig = specialMaterial.getSound();
            String[] soundParts = soundConfig.split(":");
            
            if (soundParts.length >= 1) {
                String soundName = soundParts[0];
                float volume = soundParts.length >= 2 ? Float.parseFloat(soundParts[1]) : 1.0f;
                float pitch = soundParts.length >= 3 ? Float.parseFloat(soundParts[2]) : 1.0f;
                
                // Sử dụng SoundCompatibility để tránh IncompatibleClassChangeError
                com.hongminh54.storage.Utils.SoundCompatibility.playSound(player, soundName, volume, pitch);
            }
            
            // Phát hiệu ứng hạt
            String particleConfig = specialMaterial.getParticle();
            ParticleEffect.spawnParticleFromConfig(player.getLocation(), particleConfig);
            
            // Lấy giá trị hiển thị thông báo từ notifications.obtain.show_message thay vì effects.show_message
            boolean shouldShowMessage = Boolean.parseBoolean(notificationMessages.getOrDefault("obtain.show_message", "true"));
            
            // Debug log
            if (Storage.getStorage().getLogger().isLoggable(Level.FINE)) {
                Storage.getStorage().getLogger().fine("Cấu hình notifications.obtain.show_message: " + shouldShowMessage);
            }
            
            if (shouldShowMessage) {
                String itemName = itemStack.hasItemMeta() && itemStack.getItemMeta().hasDisplayName() 
                        ? itemStack.getItemMeta().getDisplayName() 
                        : itemStack.getType().name();
                
                String message = getMessage("obtain.player", messageFormat);
                message = message.replace("#item_name#", itemName);
                player.sendMessage(Chat.colorize(message));
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi phát hiệu ứng nhận khoáng sản đặc biệt: " + e.getMessage());
        }
    }
    
    /**
     * Lớp chứa kết quả khi thử tạo tài nguyên đặc biệt
     */
    public static class SpecialMaterialResult {
        private final SpecialMaterial specialMaterial;
        private final ItemStack itemStack;

        public SpecialMaterialResult(SpecialMaterial specialMaterial, ItemStack itemStack) {
            this.specialMaterial = specialMaterial;
            this.itemStack = itemStack;
        }

        public SpecialMaterial getSpecialMaterial() {
            return specialMaterial;
        }
        
        public ItemStack getItemStack() {
            return itemStack;
        }
    }
    
    /**
     * Lớp đại diện cho một tài nguyên đặc biệt
     */
    public static class SpecialMaterial {
        private final String id;
        private final String fromBlock;
        private final String displayName;
        private final List<String> lore;
        private final String materialStr;
        private final byte data;
        private final boolean glow;
        private final double chance;
        private final String sound;
        private final String particle;
        private final MiningEvent.EventType eventOnly;
        private final int customModelData;
        
        /**
         * Tạo một đối tượng tài nguyên đặc biệt mới với đầy đủ thông tin
         */
        public SpecialMaterial(String id, String fromBlock, String displayName, List<String> lore,
                          String materialStr, byte data, boolean glow, double chance,
                          String sound, String particle, MiningEvent.EventType eventOnly, int customModelData) {
            this.id = id;
            this.fromBlock = fromBlock;
            this.displayName = displayName;
            this.lore = lore;
            this.materialStr = materialStr;
            this.data = data;
            this.glow = glow;
            this.chance = chance;
            this.sound = sound;
            this.particle = particle;
            this.eventOnly = eventOnly;
            this.customModelData = customModelData;
        }
        
        /**
         * Tạo một đối tượng tài nguyên đặc biệt mới mà có thể xuất hiện mọi lúc
         */
        public SpecialMaterial(String id, String fromBlock, String displayName, List<String> lore,
                          String materialStr, byte data, boolean glow, double chance,
                          String sound, String particle) {
            this(id, fromBlock, displayName, lore, materialStr, data, glow, chance, sound, particle, null, 0);
        }

        /**
         * Tạo một đối tượng tài nguyên đặc biệt mới mà có thể xuất hiện mọi lúc (không có custom model data)
         */
        public SpecialMaterial(String id, String fromBlock, String displayName, List<String> lore,
                          String materialStr, byte data, boolean glow, double chance,
                          String sound, String particle, MiningEvent.EventType eventOnly) {
            this(id, fromBlock, displayName, lore, materialStr, data, glow, chance, sound, particle, eventOnly, 0);
        }
        
        /**
         * Tạo ItemStack từ khoáng sản đặc biệt
         * @return ItemStack đã được tùy chỉnh
         */
        public ItemStack createItemStack() {
            Material material;
            try {
                // Sử dụng MaterialCompatibility để tương thích đa phiên bản
                material = com.hongminh54.storage.Utils.MaterialCompatibility.getMaterialSafely(materialStr);
                if (material == null) {
                    material = Material.STONE; // Fallback
                }
            } catch (Exception e) {
                // Mặc định nếu không tìm thấy vật liệu
                material = Material.STONE;
            }
            
            ItemStack item = new ItemStack(material, 1);
            ItemMeta meta = item.getItemMeta();
            
            if (meta != null) {
                // Đặt tên hiển thị
                meta.setDisplayName(Chat.colorizewp(displayName));
                
                // Đặt lore
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(Chat.colorizewp(line));
                }
                meta.setLore(coloredLore);
                
                // Thêm hiệu ứng phát sáng nếu được bật - tương thích đa phiên bản
                if (glow) {
                    Enchantment unbreaking = com.hongminh54.storage.Utils.MaterialCompatibility.getCompatibleEnchantment("UNBREAKING", "DURABILITY");
                    meta.addEnchant(unbreaking, 1, true);
                    meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                }
                
                // Kiểm tra phiên bản Minecraft trước khi gọi setCustomModelData (1.14+)
                if (customModelData > 0) {
                    try {
                        // Kiểm tra xem phiên bản có hỗ trợ Custom Model Data không (1.14+)
                        NMSAssistant nmsAssistant = new NMSAssistant();
                        if (nmsAssistant.isVersionGreaterThanOrEqualTo(14)) {
                            // Sử dụng reflection để gọi phương thức setCustomModelData an toàn
                            try {
                                java.lang.reflect.Method setCustomModelDataMethod = meta.getClass().getMethod("setCustomModelData", Integer.class);
                                setCustomModelDataMethod.invoke(meta, customModelData);
                                
                                if (Storage.getStorage().isDebug()) {
                                    Storage.getStorage().getLogger().info("Đã áp dụng CustomModelData " + customModelData + " cho khoáng sản đặc biệt " + id);
                                }
                            } catch (NoSuchMethodException | SecurityException | IllegalAccessException | java.lang.reflect.InvocationTargetException e) {
                                // Bỏ qua và ghi log nếu có lỗi
                                if (Storage.getStorage().isDebug()) {
                                    Storage.getStorage().getLogger().warning("Không thể áp dụng CustomModelData: " + e.getMessage());
                                }
                            }
                        } else {
                            // CustomModelData không khả dụng trong phiên bản này
                            if (Storage.getStorage().isDebug()) {
                                Storage.getStorage().getLogger().warning("CustomModelData không được hỗ trợ trong phiên bản Minecraft này (cần 1.14+)");
                            }
                        }
                    } catch (Exception e) {
                        // Bỏ qua nếu có lỗi kiểm tra phiên bản
                        Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra phiên bản Minecraft: " + e.getMessage());
                    }
                }
                
                item.setItemMeta(meta);
            }
            
            return item;
        }
        
        // Các phương thức getter
        
        public String getId() {
            return id;
        }
        
        public String getFromBlock() {
            return fromBlock;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public List<String> getLore() {
            return lore;
        }
        
        public double getChance() {
            return chance;
        }
        
        public String getSound() {
            return sound;
        }
        
        public String getParticle() {
            return particle;
        }
        
        /**
         * Lấy loại sự kiện mà khoáng sản chỉ xuất hiện trong đó
         * @return EventType hoặc null nếu xuất hiện trong mọi sự kiện
         */
        public MiningEvent.EventType getEventOnly() {
            return eventOnly;
        }
        
        /**
         * Lấy giá trị custom model data
         * @return Giá trị custom model data hoặc 0 nếu không có
         */
        public int getCustomModelData() {
            return customModelData;
        }
    }
    
    /**
     * Tìm tài nguyên đặc biệt theo ID
     * @param id ID của tài nguyên đặc biệt cần tìm
     * @return SpecialMaterial nếu tìm thấy, null nếu không
     */
    public static SpecialMaterial getSpecialMaterialById(String id) {
        if (id == null || id.isEmpty()) return null;

        // Tìm trong tất cả các khoáng sản đặc biệt
        for (List<SpecialMaterial> materialList : allTypeMaterials.values()) {
            for (SpecialMaterial material : materialList) {
                if (material.getId().equalsIgnoreCase(id)) {
                    return material;
                }
            }
        }

        // Tìm trong các khoáng sản chỉ xuất hiện trong sự kiện
        for (List<SpecialMaterial> materialList : eventOnlyMaterials.values()) {
            for (SpecialMaterial material : materialList) {
                if (material.getId().equalsIgnoreCase(id)) {
                    return material;
                }
            }
        }

        return null;
    }
    
    /**
     * Lấy danh sách ID của tất cả các tài nguyên đặc biệt
     * @return Danh sách ID
     */
    public static List<String> getAllSpecialMaterialIds() {
        List<String> ids = new ArrayList<>();

        // Lấy từ tất cả các khoáng sản đặc biệt
        for (List<SpecialMaterial> materialList : allTypeMaterials.values()) {
            for (SpecialMaterial material : materialList) {
                if (!ids.contains(material.getId())) {
                    ids.add(material.getId());
                }
            }
        }

        // Lấy từ các khoáng sản chỉ xuất hiện trong sự kiện
        for (List<SpecialMaterial> materialList : eventOnlyMaterials.values()) {
            for (SpecialMaterial material : materialList) {
                if (!ids.contains(material.getId())) {
                    ids.add(material.getId());
                }
            }
        }

        return ids;
    }
    
    /**
     * Tạo và cấp khoáng sản đặc biệt cho người chơi
     * @param player Người chơi nhận khoáng sản
     * @param oreId ID của khoáng sản đặc biệt
     * @param amount Số lượng
     * @param playEffects Có phát hiệu ứng không
     * @return true nếu thành công, false nếu thất bại
     */
    public static boolean giveSpecialMaterialToPlayer(Player player, String materialId, int amount, boolean playEffects) {
        if (player == null || materialId == null || amount <= 0) {
            return false;
        }

        // Tìm khoáng sản đặc biệt
        SpecialMaterial specialMaterial = getSpecialMaterialById(materialId);
        if (specialMaterial == null) {
            return false;
        }

        // Tạo ItemStack
        ItemStack itemStack = specialMaterial.createItemStack();
        itemStack.setAmount(amount);
        
        // Thêm vào túi đồ của người chơi hoặc thả xuống đất
        boolean success = false;
        if (hasInventorySpace(player)) {
            player.getInventory().addItem(itemStack);
            success = true;
        } else {
            // Thả xuống đất nếu không còn chỗ trong túi đồ
            player.getWorld().dropItemNaturally(player.getLocation(), itemStack);
            success = true;
        }
        
        // Phát hiệu ứng nếu được yêu cầu và thành công
        if (success && playEffects) {
            playEffects(player, specialMaterial, itemStack);
        }
        
        return success;
    }
    
    /**
     * Lấy danh sách ID của các tài nguyên đặc biệt chỉ xuất hiện trong một loại sự kiện cụ thể
     * @param eventType Loại sự kiện cần kiểm tra
     * @return Danh sách ID của các tài nguyên đặc biệt chỉ xuất hiện trong sự kiện này
     */
    public static List<String> getEventOnlyMaterials(MiningEvent.EventType eventType) {
        if (eventType == null || eventType == MiningEvent.EventType.NONE) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();

        // Kiểm tra các khoáng sản chỉ xuất hiện trong sự kiện cụ thể
        if (eventOnlyMaterials.containsKey(eventType)) {
            List<SpecialMaterial> materials = eventOnlyMaterials.get(eventType);
            for (SpecialMaterial material : materials) {
                result.add(material.getDisplayName());
            }
        }

        // Thêm cả các khoáng sản khác mà có eventOnly = eventType
        for (List<SpecialMaterial> materialList : allTypeMaterials.values()) {
            for (SpecialMaterial material : materialList) {
                if (material.getEventOnly() == eventType && !result.contains(material.getDisplayName())) {
                    result.add(material.getDisplayName());
                }
            }
        }

        return result;
    }
    
    /**
     * Reload toàn bộ cài đặt khoáng sản đặc biệt từ file cấu hình
     */
    public static void reload() {
        try {
            // Làm mới cache file config
            File.invalidateSpecialMaterialsCache();

            // Tải lại cấu hình
            loadConfig();

            // Đảm bảo cập nhật hệ số nhân sự kiện
            initializeEventMultipliers();

            // Log kết quả tải
            Storage.getStorage().getLogger().info("Đã tải lại cấu hình khoáng sản đặc biệt thành công!");
            Storage.getStorage().getLogger().info("- Trạng thái hoạt động: " + (enabled ? "§aBật" : "§cTắt"));
            Storage.getStorage().getLogger().info("- Event Multipliers: " + (eventMultipliersEnabled ? "§aBật" : "§cTắt"));
            Storage.getStorage().getLogger().info("- Số lượng khoáng sản đặc biệt: " + specialMaterials.size());
            
            // Khắc phục sự cố khi khởi động server
            ensureListenerRegistered();
            
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi tải lại cấu hình khoáng sản đặc biệt: " + e.getMessage(), e);
            enabled = false;
        }
    }
    
    /**
     * Đảm bảo SpecialMaterialListener được đăng ký đúng cách
     */
    private static void ensureListenerRegistered() {
        try {
            // Tạo và đăng ký lại listener nếu cần
            if (enabled) {
                // Đăng ký mới SpecialMaterialListener thay vì cố kiểm tra sự tồn tại
                Storage.getStorage().getLogger().info("Đảm bảo đăng ký SpecialMaterialListener...");
                Storage.getStorage().registerEvents(new SpecialMaterialListener());
                Storage.getStorage().getLogger().info("Đã đăng ký thành công SpecialMaterialListener!");
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.WARNING, "Không thể đảm bảo đăng ký SpecialMaterialListener: " + e.getMessage(), e);
        }
    }
    
    /**
     * Lưu cấu hình tài nguyên đặc biệt
     */
    public static void saveConfig() {
        try {
            com.hongminh54.storage.Utils.File.saveSpecialMaterials();
            Storage.getStorage().getLogger().info("Đã lưu cấu hình tài nguyên đặc biệt thành công.");
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lưu cấu hình tài nguyên đặc biệt: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 