package com.hongminh54.storage.GUI;

import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Utils.*;
import com.hongminh54.storage.Storage;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * GUI chuyên dụng cho chế độ transfer - khác biệt với PersonalStorage thường
 */
public class TransferStorageGUI implements IGUI, Listener {

    private final Player sender;
    private final Player receiver;
    private final FileConfiguration config;
    private Inventory inventory;
    private boolean listenerRegistered = false;

    public TransferStorageGUI(Player sender, Player receiver) {
        this.sender = sender;
        this.receiver = receiver;
        this.config = File.getGUIConfig("transfer_storage");

        // Validate config và input
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config transfer_storage.yml có vấn đề cho TransferStorageGUI");
        }
        if (!validateInput()) {
            Storage.getStorage().getLogger().warning("Input không hợp lệ cho TransferStorageGUI");
        }

        // Đăng ký Listener khi tạo GUI
        registerListener();
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Tạo title từ config
        String title = config.getString("title", "&6Chuyển tài nguyên &8| &e{receiver}")
                .replace("{receiver}", receiver.getName());

        inventory = Bukkit.createInventory(this, config.getInt("size", 6) * 9,
            GUIText.format(title));

        // Phát âm thanh từ config
        try {
            String openSound = config.getString("sounds.open", "BLOCK_NOTE_BLOCK_PLING:1.0:1.5");
            SoundManager.playSoundFromConfig(sender, openSound);
        } catch (Exception e) {
            // Fallback âm thanh
            try {
                String fallbackSound = config.getString("sounds.open_fallback", "BLOCK_CHEST_OPEN:0.5:1.0");
                SoundManager.playSoundFromConfig(sender, fallbackSound);
            } catch (Exception ex) {
                // Bỏ qua lỗi âm thanh
            }
        }

        // Thêm các item khoáng sản với giao diện transfer đặc biệt
        addTransferMinerals(inventory);
        
        // Thêm các item điều khiển đặc biệt cho transfer
        addTransferControls(inventory);
        
        // Thêm item trang trí với theme transfer
        addTransferDecorations(inventory);

        return inventory;
    }

    /**
     * Thêm các khoáng sản với giao diện transfer đặc biệt
     */
    private void addTransferMinerals(Inventory inventory) {
        String slotConfig = config.getString("items.storage_item.slot", "10,11,12,13,14,15,16,19,20,21,22,23,24,25,31");
        String[] slots = slotConfig.replace(" ", "").split(",");
        List<String> materials = MineManager.getPluginBlocks();

        for (int i = 0; i < materials.size() && i < slots.length; i++) {
            String material = materials.get(i);
            int slot = com.hongminh54.storage.Utils.Number.getInteger(slots[i]);

            // Tạo item với theme transfer từ config
            ItemStack item = createTransferMineralItem(material);

            // Tạo InteractiveItem với click handler cho transfer
            InteractiveItem interactiveItem = new InteractiveItem(item, slot).onClick((player, clickType) -> {
                // Đóng GUI và mở TransferGUI cho khoáng sản cụ thể
                player.closeInventory();

                Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                    if (player.isOnline() && receiver.isOnline()) {
                        player.openInventory(new TransferGUI(player, receiver, material).getInventory());
                    } else if (player.isOnline()) {
                        player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + receiver.getName() + " &ckhông còn trực tuyến!"));
                        try {
                            String errorSound = config.getString("sounds.error", "ENTITY_VILLAGER_NO:0.5:1.0");
                            SoundManager.playSoundFromConfig(player, errorSound);
                        } catch (Exception e) {
                            // Fallback sound
                            com.hongminh54.storage.Utils.SoundCompatibility.playSound(player, "ENTITY_VILLAGER_NO", 0.5f, 1.0f);
                        }
                    }
                }, 1L);
            });

            inventory.setItem(interactiveItem.getSlot(), interactiveItem);
        }
    }

    /**
     * Tạo item khoáng sản với theme transfer từ config
     */
    private ItemStack createTransferMineralItem(String material) {
        String materialType = MineManager.getMaterial(material);
        String displayName = File.getConfig().getString("items." + material, material.split(";")[0]);
        int currentAmount = MineManager.getPlayerBlock(sender, material);
        int maxStorage = File.getConfig().getInt("settings.default_max_storage", 1000000);

        // Tạo item với material type
        ItemStack item = ItemManager.getItemConfig(sender, materialType, displayName, config.getConfigurationSection("items.storage_item"));
        ItemMeta meta = item.getItemMeta();

        // Tên hiển thị từ config
        String nameTemplate;
        if (currentAmount > 0) {
            nameTemplate = config.getString("items.storage_item.name_available", "&a&l⚡ {material_name} &7(&f{current_amount}&7)");
        } else {
            nameTemplate = config.getString("items.storage_item.name_empty", "&7&l⚡ {material_name} &8(&f0&8)");
        }

        String finalName = nameTemplate
                .replace("{material_name}", displayName)
                .replace("{current_amount}", String.valueOf(currentAmount));
        meta.setDisplayName(Chat.colorize(finalName));

        // Lore từ config
        List<String> loreTemplate;
        if (currentAmount > 0) {
            loreTemplate = config.getStringList("items.storage_item.lore_available");
        } else {
            loreTemplate = config.getStringList("items.storage_item.lore_empty");
        }

        List<String> lore = new ArrayList<>();
        for (String line : loreTemplate) {
            String processedLine = line
                    .replace("{current_amount}", String.valueOf(currentAmount))
                    .replace("{max_storage}", String.valueOf(maxStorage))
                    .replace("{receiver}", receiver.getName())
                    .replace("{material_name}", displayName);
            lore.add(Chat.colorize(processedLine));
        }
        meta.setLore(lore);

        // Thêm enchant glow từ config
        if (currentAmount > 0 && config.getBoolean("effects.glow_available", true)) {
            try {
                if (config.getConfigurationSection("items.storage_item.enchants_available") != null) {
                    for (String enchant : config.getConfigurationSection("items.storage_item.enchants_available").getKeys(false)) {
                        int level = config.getInt("items.storage_item.enchants_available." + enchant, 1);
                        org.bukkit.enchantments.Enchantment enchantment = MaterialCompatibility.getCompatibleEnchantment(enchant, enchant);
                        if (enchantment != null) {
                            meta.addEnchant(enchantment, level, true);
                        }
                    }
                }
                meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
            } catch (Exception e) {
                // Bỏ qua lỗi enchant
            }
        }

        // Áp dụng custom model data từ config
        applyCustomModelData(meta, "items.storage_item.custom-model-data");

        // Áp dụng unbreakable từ config
        try {
            if (config.getBoolean("items.storage_item.unbreakable", true)) {
                meta.setUnbreakable(true);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi unbreakable
        }

        item.setItemMeta(meta);
        return item;
    }

    /**
     * Thêm các nút điều khiển đặc biệt cho transfer
     */
    private void addTransferControls(Inventory inventory) {
        // Nút chuyển nhiều loại
        addMultiTransferButton(inventory);
        
        // Nút quay lại
        addBackButton(inventory);
        
        // Nút thông tin người nhận
        addReceiverInfoButton(inventory);
    }

    /**
     * Thêm nút chuyển nhiều loại tài nguyên từ config
     */
    private void addMultiTransferButton(Inventory inventory) {
        // Lấy thông tin từ config
        String materialName = config.getString("items.multi_transfer_button.material", "HOPPER");
        int slot = config.getInt("items.multi_transfer_button.slot", 49);

        ItemStack item = createCompatibleItemStack(materialName);
        ItemMeta meta = item.getItemMeta();

        // Tên từ config
        String name = config.getString("items.multi_transfer_button.name", "&6&lChuyển Nhiều Loại")
                .replace("{receiver}", receiver.getName());
        meta.setDisplayName(Chat.colorize(name));

        // Lore từ config
        List<String> loreTemplate = config.getStringList("items.multi_transfer_button.lore");
        List<String> lore = new ArrayList<>();
        for (String line : loreTemplate) {
            String processedLine = line.replace("{receiver}", receiver.getName());
            lore.add(Chat.colorize(processedLine));
        }
        meta.setLore(lore);

        // Enchants từ config
        if (config.getBoolean("effects.glow_buttons", true) && config.getConfigurationSection("items.multi_transfer_button.enchants") != null) {
            try {
                for (String enchant : config.getConfigurationSection("items.multi_transfer_button.enchants").getKeys(false)) {
                    int level = config.getInt("items.multi_transfer_button.enchants." + enchant, 1);
                    org.bukkit.enchantments.Enchantment enchantment = MaterialCompatibility.getCompatibleEnchantment(enchant, enchant);
                    if (enchantment != null) {
                        meta.addEnchant(enchantment, level, true);
                    }
                }
                meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
            } catch (Exception e) {
                // Bỏ qua lỗi enchant
            }
        }

        // Custom model data từ config
        applyCustomModelData(meta, "items.multi_transfer_button.custom-model-data");

        item.setItemMeta(meta);

        InteractiveItem multiButton = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            player.closeInventory();

            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                if (player.isOnline() && receiver.isOnline()) {
                    player.openInventory(new MultiTransferGUI(player, receiver).getInventory());
                } else if (player.isOnline()) {
                    player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + receiver.getName() + " &ckhông còn trực tuyến!"));
                    try {
                        String errorSound = config.getString("sounds.error", "ENTITY_VILLAGER_NO:0.5:1.0");
                        SoundManager.playSoundFromConfig(player, errorSound);
                    } catch (Exception e) {
                        // Fallback sound
                        player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 0.5f, 1.0f);
                    }
                }
            }, 1L);
        });

        inventory.setItem(multiButton.getSlot(), multiButton);
    }

    /**
     * Thêm nút quay lại kho bình thường từ config
     */
    private void addBackButton(Inventory inventory) {
        // Lấy thông tin từ config
        String materialName = config.getString("items.back_button.material", "BARRIER");
        int slot = config.getInt("items.back_button.slot", 45);

        ItemStack item = createCompatibleItemStack(materialName);
        ItemMeta meta = item.getItemMeta();

        // Tên từ config
        String name = config.getString("items.back_button.name", "&c&l← Quay lại");
        meta.setDisplayName(Chat.colorize(name));

        // Lore từ config
        List<String> loreTemplate = config.getStringList("items.back_button.lore");
        List<String> lore = new ArrayList<>();
        for (String line : loreTemplate) {
            lore.add(Chat.colorize(line));
        }
        meta.setLore(lore);

        // Custom model data từ config
        applyCustomModelData(meta, "items.back_button.custom-model-data");

        item.setItemMeta(meta);

        InteractiveItem backButton = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            player.closeInventory();

            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                if (player.isOnline()) {
                    player.openInventory(new PersonalStorage(player).getInventory());
                }
            }, 1L);
        });

        inventory.setItem(backButton.getSlot(), backButton);
    }

    /**
     * Thêm nút thông tin người nhận từ config
     */
    private void addReceiverInfoButton(Inventory inventory) {
        // Lấy thông tin từ config
        String materialName = config.getString("items.receiver_info.material", "PLAYER_HEAD");
        int slot = config.getInt("items.receiver_info.slot", 53);

        ItemStack item = createCompatibleItemStack(materialName);
        ItemMeta meta = item.getItemMeta();

        // Tên từ config
        String name = config.getString("items.receiver_info.name", "&e&l👤 {receiver}")
                .replace("{receiver}", receiver.getName());
        meta.setDisplayName(Chat.colorize(name));

        // Trạng thái online/offline từ config
        String statusOnline = config.getString("status_messages.online", "&aOnline");
        String statusOffline = config.getString("status_messages.offline", "&cOffline");
        String currentStatus = receiver.isOnline() ? statusOnline : statusOffline;

        // Lore từ config
        List<String> loreTemplate = config.getStringList("items.receiver_info.lore");
        List<String> lore = new ArrayList<>();
        for (String line : loreTemplate) {
            String processedLine = line
                    .replace("{receiver}", receiver.getName())
                    .replace("{status}", currentStatus);
            lore.add(Chat.colorize(processedLine));
        }
        meta.setLore(lore);

        // Custom model data từ config
        applyCustomModelData(meta, "items.receiver_info.custom-model-data");

        item.setItemMeta(meta);

        InteractiveItem infoButton = new InteractiveItem(item, slot);
        inventory.setItem(infoButton.getSlot(), infoButton);
    }

    /**
     * Thêm item trang trí với theme transfer từ config
     */
    private void addTransferDecorations(Inventory inventory) {
        String decorateSlots = config.getString("items.decorates.slot",
            "0,1,2,3,4,5,6,7,8,9,17,18,26,27,35,36,44,46,47,48,50,51,52");
        String[] slots = decorateSlots.replace(" ", "").split(",");

        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr);

                // Lấy thông tin từ config
                String materialName = config.getString("items.decorates.material", "ORANGE_STAINED_GLASS_PANE");
                ItemStack decorateItem = createCompatibleItemStack(materialName);
                ItemMeta meta = decorateItem.getItemMeta();

                // Tên từ config
                String name = config.getString("items.decorates.name", "&6&l⚡ CHUYỂN KHOÁNG SẢN ⚡");
                meta.setDisplayName(Chat.colorize(name));

                // Lore từ config
                List<String> loreTemplate = config.getStringList("items.decorates.lore");
                List<String> lore = new ArrayList<>();
                for (String line : loreTemplate) {
                    String processedLine = line.replace("{receiver}", receiver.getName());
                    lore.add(Chat.colorize(processedLine));
                }
                meta.setLore(lore);

                // Các thuộc tính từ config
                try {
                    if (config.getBoolean("items.decorates.unbreakable", true)) {
                        meta.setUnbreakable(true);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi
                }

                // Custom model data từ config
                applyCustomModelData(meta, "items.decorates.custom-model-data");

                // Enchants từ config
                if (config.getConfigurationSection("items.decorates.enchants") != null) {
                    try {
                        for (String enchant : config.getConfigurationSection("items.decorates.enchants").getKeys(false)) {
                            int level = config.getInt("items.decorates.enchants." + enchant, 1);
                            org.bukkit.enchantments.Enchantment enchantment = MaterialCompatibility.getCompatibleEnchantment(enchant, enchant);
                            if (enchantment != null) {
                                meta.addEnchant(enchantment, level, true);
                            }
                        }
                        meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
                    } catch (Exception e) {
                        // Bỏ qua lỗi enchant
                    }
                }

                decorateItem.setItemMeta(meta);

                InteractiveItem decorateInteractive = new InteractiveItem(decorateItem, slot);
                inventory.setItem(decorateInteractive.getSlot(), decorateInteractive);

            } catch (NumberFormatException e) {
                // Bỏ qua slot không hợp lệ
            }
        }
    }

    /**
     * Validate cấu hình GUI
     * @return true nếu config hợp lệ
     */
    private boolean validateConfig() {
        try {
            if (config == null) {
                handleError("Config file transfer_storage.yml not found", false);
                return false;
            }

            // Kiểm tra các section cần thiết
            if (!config.contains("title")) {
                handleError("Missing title in transfer_storage.yml", false);
                return false;
            }

            if (!config.contains("size")) {
                handleError("Missing size in transfer_storage.yml", false);
                return false;
            }

            if (!config.contains("items")) {
                handleError("Missing items section in transfer_storage.yml", false);
                return false;
            }

            // Kiểm tra các item sections
            String[] requiredItems = {"decorates", "storage_item", "multi_transfer_button", "back_button", "receiver_info"};
            for (String item : requiredItems) {
                if (!config.contains("items." + item)) {
                    handleError("Missing items." + item + " section in transfer_storage.yml", false);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            handleError("Error validating config: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * Validate input parameters
     * @return true nếu input hợp lệ
     */
    private boolean validateInput() {
        try {
            if (sender == null) {
                handleError("Sender cannot be null", false);
                return false;
            }

            if (receiver == null) {
                handleError("Receiver cannot be null", false);
                return false;
            }

            if (!sender.isOnline()) {
                handleError("Sender must be online", false);
                return false;
            }

            if (sender.equals(receiver)) {
                handleError("Sender cannot transfer to themselves", false);
                return false;
            }

            return true;
        } catch (Exception e) {
            handleError("Error validating input: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * Xử lý lỗi và hiển thị thông báo
     * @param error Thông điệp lỗi
     * @param showToPlayer Có hiển thị cho người chơi không
     */
    private void handleError(String error, boolean showToPlayer) {
        Storage.getStorage().getLogger().warning("TransferStorageGUI Error: " + error);
        if (showToPlayer && sender != null && sender.isOnline()) {
            // Chỉ phát âm thanh lỗi mà không hiển thị tin nhắn lỗi cho người chơi
            try {
                String failSound = config.getString("sounds.error", "ENTITY_VILLAGER_NO:0.5:1.0");
                SoundManager.playSoundFromConfig(sender, failSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
        }
    }

    /**
     * Áp dụng custom model data cho ItemMeta nếu phiên bản hỗ trợ
     * @param meta ItemMeta cần áp dụng
     * @param configPath Đường dẫn config cho custom model data
     */
    private void applyCustomModelData(ItemMeta meta, String configPath) {
        if (meta == null) return;

        try {
            int customModelData = config.getInt(configPath, 0);
            if (customModelData > 0) {
                // Sử dụng AdvancedCompatibility để set custom model data an toàn
                AdvancedCompatibility.setCustomModelData(meta, customModelData);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi custom model data
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể áp dụng custom model data: " + e.getMessage());
            }
        }
    }

    /**
     * Tạo ItemStack tương thích với phiên bản
     * @param materialName Tên material
     * @return ItemStack tương thích
     */
    private ItemStack createCompatibleItemStack(String materialName) {
        // Sử dụng function mới từ MaterialCompatibility
        return MaterialCompatibility.createCompatibleItemStack(materialName);
    }

    /**
     * Lấy material fallback cho tương thích phiên bản
     * @param materialName Tên material gốc
     * @return Tên material fallback
     */
    private String getMaterialFallback(String materialName) {
        // Mapping cho các material có tên khác nhau giữa các phiên bản
        switch (materialName.toUpperCase()) {
            case "ORANGE_STAINED_GLASS_PANE":
                return "STAINED_GLASS_PANE"; // 1.12.2 fallback
            case "PLAYER_HEAD":
                return "SKULL_ITEM"; // 1.12.2 fallback
            default:
                return materialName;
        }
    }

    /**
     * Đăng ký listener cho GUI
     */
    private void registerListener() {
        if (!listenerRegistered) {
            Bukkit.getPluginManager().registerEvents(this, Storage.getStorage());
            listenerRegistered = true;
        }
    }

    /**
     * Hủy đăng ký listener
     */
    private void unregisterListener() {
        if (listenerRegistered) {
            HandlerList.unregisterAll(this);
            listenerRegistered = false;
        }
    }

    /**
     * Xử lý sự kiện click vào inventory
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        // Sử dụng InventoryCompatibility để lấy top inventory một cách an toàn
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory == null || !topInventory.equals(inventory)) {
            return;
        }

        // Hủy tất cả sự kiện click trong GUI
        event.setCancelled(true);

        // Cập nhật inventory để đảm bảo UI ổn định
        if (event.getWhoClicked() instanceof Player) {
            ((Player) event.getWhoClicked()).updateInventory();
        }

        // Xử lý các click vào inventory này nếu ItemStack không null và thuộc inventory trên cùng
        // Chỉ xử lý click vào inventory của GUI
        if (event.getCurrentItem() == null || event.getClickedInventory() == null || event.getClickedInventory() != inventory) {
            return;
        }

        // Các nút tương tác đã được tạo với InteractiveItem và xử lý riêng thông qua class GUI
    }

    /**
     * Xử lý sự kiện đóng inventory
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().equals(inventory)) {
            // Hủy đăng ký listener khi đóng giao diện
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                unregisterListener();
            }, 1L);
        }
    }

    /**
     * Xử lý sự kiện kéo vật phẩm trong inventory
     * Ngăn người chơi kéo vật phẩm trong GUI
     */
    @EventHandler
    public void onInventoryDrag(InventoryDragEvent event) {
        // Sử dụng InventoryCompatibility để lấy top inventory một cách an toàn
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory != null && topInventory.equals(inventory)) {
            // Hủy tất cả các sự kiện kéo vật phẩm liên quan đến GUI này
            event.setCancelled(true);

            // Cập nhật inventory ngay lập tức để đảm bảo UI ổn định
            if (event.getWhoClicked() instanceof Player) {
                ((Player) event.getWhoClicked()).updateInventory();
            }
        }
    }

    public Player getSender() {
        return sender;
    }

    public Player getReceiver() {
        return receiver;
    }

    public FileConfiguration getConfig() {
        return config;
    }

    public Inventory getInventoryInstance() {
        return inventory;
    }
}
