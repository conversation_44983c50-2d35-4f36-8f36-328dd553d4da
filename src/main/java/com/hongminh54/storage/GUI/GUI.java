package com.hongminh54.storage.GUI;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.Nullable;

import com.hongminh54.storage.GUI.listeners.GUIClickListener;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Utils.MaterialCompatibility;

public class GUI {
    /**
     * The item mapper, which is used to recognize which item was clicked based on NBT tag.
     */
    private static final HashMap<UUID, InteractiveItem> itemMapper = new HashMap<>();
    
    /**
     * The interactive items map for slots in GUI
     */
    private static final Map<Integer, InteractiveItem> interactiveItems = new HashMap<>();

    public static HashMap<UUID, InteractiveItem> getItemMapper() {
        return itemMapper;
    }
    
    public static Map<Integer, InteractiveItem> getInteractiveItems() {
        return interactiveItems;
    }

    /**
     * Important to call in onEnable to register the GUI listener.
     *
     * @param plugin The plugin instance.
     */
    public static void register(Plugin plugin) {
        Bukkit.getPluginManager().registerEvents(new GUIClickListener(), plugin);
    }
    
    /**
     * Create an inventory with the specified title and size for a player
     * 
     * @param player The player to create the inventory for
     * @param size The size of the inventory
     * @param title The title of the inventory
     * @return The created inventory
     */
    public static Inventory createInventory(Player player, int size, String title) {
        return Bukkit.createInventory(player, size, title);
    }

    /**
     * Fills the inventory with items, while also creating a border around it, with an option to disable sides and keep only the top and bottom frame.
     *
     * @param inventory   The inventory to create the border in.
     * @param fillerPanel The material to use for the inner fill, if null, it won't replace the original contents.
     * @param borderPanel The material to use for the border, if null, it won't replace the original contents. If you want the borders to be the same as filler, you have to specify the same item as in fillerPanel.
     * @param full        Whether to create a full border or just a frame.
     */
    public static void fillInventory(Inventory inventory, @Nullable ItemStack fillerPanel, @Nullable ItemStack borderPanel, boolean full) {
        for (int i = 0; i < inventory.getSize(); i++) {
            if ((i % 9 == 0 || (i - 8) % 9 == 0) && borderPanel != null)
                inventory.setItem(i, borderPanel);
            else if (full && (i < 9 || i >= inventory.getSize() - 9) && borderPanel != null)
                inventory.setItem(i, borderPanel);
            else if (fillerPanel != null)
                inventory.setItem(i, fillerPanel);
        }
    }

    /**
     * Fills the inventory with items, while also creating a border around it.
     *
     * @param inventory   The inventory to create the border in.
     * @param fillerPanel The material to use for the inner fill, if null, it won't replace the original contents.
     * @param borderPanel The material to use for the border, if null, it won't replace the original contents. If you want the borders to be the same as filler, you have to specify the same item as in fillerPanel.
     */
    public static void fillInventory(Inventory inventory, @Nullable ItemStack fillerPanel, @Nullable ItemStack borderPanel) {
        fillInventory(inventory, fillerPanel, borderPanel, true);
    }
    
    /**
     * Clear all interactive items
     */
    public static void clearInteractiveItems() {
        interactiveItems.clear();
    }
    
    /**
     * Tạo ItemStack tương thích với cả phiên bản 1.12.2 và 1.13+ cho các vật liệu phổ biến
     * trong giao diện người dùng
     *
     * @param material Tên vật liệu (sẽ được chuyển đổi phù hợp với phiên bản)
     * @param amount Số lượng item
     * @param durability Độ bền/data value (chỉ có ý nghĩa trong phiên bản 1.12.2)
     * @return ItemStack tương thích với phiên bản đang chạy
     */
    public static ItemStack createCompatibleItem(String material, int amount, short durability) {
        // Sử dụng function mới từ MaterialCompatibility
        ItemStack item = MaterialCompatibility.createCompatibleItemStack(material);
        item.setAmount(amount);

        // Áp dụng durability cho phiên bản 1.12.2
        if (MaterialCompatibility.isPre113() && durability > 0) {
            item.setDurability(durability);
        }

        return item;
    }
    
    /**
     * Tạo ItemStack kính màu (STAINED_GLASS_PANE) tương thích với cả phiên bản 1.12.2 và 1.13+
     * 
     * @param color Màu sắc (ví dụ: WHITE, GRAY, BLACK...)
     * @param amount Số lượng
     * @return ItemStack kính màu tương thích với phiên bản đang chạy
     */
    public static ItemStack createColoredGlass(String color, int amount) {
        if (MaterialCompatibility.isPre113()) {
            // Phiên bản 1.12.2: sử dụng STAINED_GLASS_PANE với data value
            byte data;
            switch(color.toUpperCase()) {
                case "WHITE": data = 0; break;
                case "ORANGE": data = 1; break;
                case "MAGENTA": data = 2; break;
                case "LIGHT_BLUE": data = 3; break;
                case "YELLOW": data = 4; break;
                case "LIME": data = 5; break;
                case "PINK": data = 6; break;
                case "GRAY": data = 7; break;
                case "LIGHT_GRAY": data = 8; break;
                case "CYAN": data = 9; break;
                case "PURPLE": data = 10; break;
                case "BLUE": data = 11; break;
                case "BROWN": data = 12; break;
                case "GREEN": data = 13; break;
                case "RED": data = 14; break;
                case "BLACK": data = 15; break;
                default: data = 0; break;
            }
            return new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), amount, data);
        } else {
            // Phiên bản 1.13+: sử dụng <COLOR>_STAINED_GLASS_PANE
            try {
                Material material = MaterialCompatibility.getMaterialSafely(color.toUpperCase() + "_STAINED_GLASS_PANE");
                if (material != null) {
                    return new ItemStack(material, amount);
                } else {
                    return new ItemStack(Material.WHITE_STAINED_GLASS_PANE, amount);
                }
            } catch (Exception e) {
                // Fallback về mặc định
                return new ItemStack(Material.WHITE_STAINED_GLASS_PANE, amount);
            }
        }
    }
}
