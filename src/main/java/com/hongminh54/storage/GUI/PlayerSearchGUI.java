package com.hongminh54.storage.GUI;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.metadata.MetadataValue;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.AdvancedCompatibility;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.InventoryCompatibility;
import com.hongminh54.storage.Utils.MaterialCompatibility;
import com.hongminh54.storage.Utils.PlayerCompatibility;
import com.hongminh54.storage.Utils.PlayerSearchChatHandler;
import com.hongminh54.storage.Utils.SoundManager;
import com.cryptomorin.xseries.XMaterial;

import org.bukkit.configuration.file.FileConfiguration;

/**
 * Giao diện tìm kiếm người chơi
 */
public class PlayerSearchGUI implements IGUI, Listener {
    private static final int MAX_PLAYER_SEARCH_RESULTS = 100; // Giới hạn kết quả tìm kiếm

    // Cache cho file cấu hình GUI
    private static FileConfiguration guiConfig = null;
    private static long lastConfigLoad = 0;
    private static final long CONFIG_CACHE_TIME = 30000; // 30 giây

    // Cache cho decorative items để tối ưu hiệu suất
    private static final Map<String, ItemStack> decorativeItemCache = new HashMap<>();
    private static final Map<String, Long> cacheTimestamps = new HashMap<>();
    private static final long CACHE_DURATION = 300000; // 5 phút

    // Đọc cấu hình từ GUI/player_search.yml
    private static FileConfiguration getGUIConfig() {
        long currentTime = System.currentTimeMillis();
        if (guiConfig == null || currentTime - lastConfigLoad > CONFIG_CACHE_TIME) {
            guiConfig = File.getGUIConfig("player_search");
            lastConfigLoad = currentTime;
        }
        return guiConfig;
    }

    /**
     * Xử lý lỗi một cách thống nhất
     * @param message Thông báo lỗi
     * @param severe Có phải lỗi nghiêm trọng không
     */
    private static void handleError(String message, boolean severe) {
        if (severe) {
            Storage.getStorage().getLogger().severe("[PlayerSearchGUI] " + message);
        } else {
            Storage.getStorage().getLogger().warning("[PlayerSearchGUI] " + message);
        }
    }

    /**
     * Xóa cache cũ để tối ưu bộ nhớ
     */
    public static void clearExpiredCache() {
        long currentTime = System.currentTimeMillis();
        cacheTimestamps.entrySet().removeIf(entry -> {
            if ((currentTime - entry.getValue()) >= CACHE_DURATION) {
                decorativeItemCache.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }

    // Đọc cấu hình từ config.yml thay vì hardcode
    private static int getPlayersPerPage() {
        FileConfiguration config = getGUIConfig();
        int configValue = config.getInt("display.max_players_per_page", 28);
        // Giới hạn tối đa 28 người chơi (4 hàng x 7 cột) để phù hợp với GUI layout
        return Math.min(configValue, 28);
    }

    private static int getItemsPerPage() {
        return getPlayersPerPage(); // Sử dụng cùng giá trị với players_per_page
    }

    // Lấy thời gian cooldown từ cấu hình
    private static long getRefreshCooldown() {
        return getGUIConfig().getLong("display.refresh_cooldown", 1500);
    }

    private final Player sender;
    private final String searchText;
    private final int page;
    private Inventory inventory;
    private boolean listenerRegistered = false;
    private long lastRefreshTime = 0;
    private final Map<String, ItemStack> playerHeadCache = new HashMap<>();
    // Thêm một Map mới cho các dữ liệu không phải ItemStack
    private final Map<String, Object> dataCache = new HashMap<>();

    /**
     * Khởi tạo giao diện tìm kiếm người chơi
     * @param sender Người gửi
     */
    public PlayerSearchGUI(Player sender) {
        this(sender, "", 0);
    }
    
    /**
     * Khởi tạo giao diện tìm kiếm người chơi
     * @param sender Người gửi
     * @param searchText Từ khóa tìm kiếm
     * @param page Trang hiện tại
     */
    public PlayerSearchGUI(Player sender, String searchText, int page) {
        this.sender = sender;
        this.searchText = searchText;
        this.page = page;

        // Kiểm tra tính hợp lệ của cấu hình trước khi khởi tạo
        if (!validateGUIConfig()) {
            handleError("Cấu hình GUI không hợp lệ, không thể khởi tạo giao diện", true);
            return;
        }

        // Đăng ký Listener khi tạo GUI
        registerListener();

        // Tối ưu hóa hiệu suất ngay từ đầu
        optimizePerformance();
    }
    
    /**
     * Đăng ký listener cho GUI
     */
    private void registerListener() {
        // Kiểm tra xem listener đã đăng ký hay chưa, và chỉ đăng ký nếu chưa
        if (!listenerRegistered) {
            try {
                // Hủy đăng ký trước nếu đã tồn tại (để tránh đăng ký nhiều lần)
                HandlerList.unregisterAll(this);
                // Đăng ký mới listener
                Bukkit.getPluginManager().registerEvents(this, Storage.getStorage());
                listenerRegistered = true;
            } catch (Exception e) {
                Storage.getStorage().getLogger().warning("Lỗi khi đăng ký listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * Hủy đăng ký listener
     */
    private void unregisterListener() {
        if (listenerRegistered) {
            try {
                // Hủy đăng ký listener
                HandlerList.unregisterAll(this);
                listenerRegistered = false;
                
                // Xóa bộ nhớ cache sau khi đóng GUI để tránh xung đột dữ liệu
                cleanupPlayerHeadCache();
                playerHeadCache.clear();
                dataCache.clear();
            } catch (Exception e) {
                Storage.getStorage().getLogger().warning("Lỗi khi hủy đăng ký listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * Dọn dẹp cache đầu người chơi để tránh rò rỉ bộ nhớ
     * Chỉ giữ lại cache cho người chơi đang online
     */
    private void cleanupPlayerHeadCache() {
        try {
            // Giữ lại cache cho tối đa 100 người chơi để tiết kiệm bộ nhớ
            if (playerHeadCache.size() > 100) {
                List<String> keysToRemove = new ArrayList<>();
                
                // Chỉ giữ lại cache cho người chơi đang online
                for (String key : playerHeadCache.keySet()) {
                    if (key.startsWith("player_")) {
                        String playerName = key.substring(7); // Bỏ "player_"
                        Player player = Bukkit.getPlayer(playerName);
                        if (player == null || !player.isOnline()) {
                            keysToRemove.add(key);
                        }
                    }
                }
                
                // Xóa các key không cần thiết
                for (String key : keysToRemove) {
                    playerHeadCache.remove(key);
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi để đảm bảo GUI vẫn hoạt động
            Storage.getStorage().getLogger().warning("Lỗi khi dọn dẹp cache đầu người chơi: " + e.getMessage());
        }
    }

    /**
     * Phát âm thanh an toàn cho người chơi
     * @param player Người chơi
     * @param sound Âm thanh
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    private void playSound(Player player, Sound sound, float volume, float pitch) {
        try {
            String soundName = sound.name();
            
            // Sử dụng SoundCompatibility để xử lý tương thích đa phiên bản
            com.hongminh54.storage.Utils.SoundCompatibility.playSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // Bỏ qua nếu không hỗ trợ âm thanh
        }
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        FileConfiguration config = getGUIConfig();

        // Tạo tiêu đề giao diện từ cấu hình
        String title = config.getString("title", "&8Tìm kiếm người chơi");
        int size = config.getInt("size", 6) * 9;
        inventory = GUI.createInventory(sender, size, GUIText.format(title));

        // Thêm viền trang trí cho GUI trước
        setupBorder();

        // Thêm danh sách người chơi
        addPlayersList();

        // Thêm các nút phân trang
        addPaginationButtons();

        // Thêm các nút chức năng
        addFunctionButtons();

        // Phát âm thanh mở GUI từ cấu hình
        playConfiguredSound(sender, "open");

        return inventory;
    }

    /**
     * Thiết lập viền cho giao diện từ cấu hình
     */
    private void setupBorder() {
        FileConfiguration config = getGUIConfig();

        // Đọc cấu hình viền - hỗ trợ cả decorates và border (fallback)
        String borderSlots = config.getString("items.decorates.slot",
            config.getString("items.border.slot", "0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 52, 53"));
        String borderName = config.getString("items.decorates.name",
            config.getString("items.border.name", "&7 "));
        String borderMaterial = config.getString("items.decorates.material",
            config.getString("items.border.material", "GRAY_STAINED_GLASS_PANE"));

        // Tạo vật phẩm viền - ưu tiên decorates, fallback border
        String configPath = config.contains("items.decorates") ? "items.decorates" : "items.border";
        ItemStack borderItem = createCompatibleDecorativeItem(configPath);

        // Thiết lập meta
        ItemMeta meta = borderItem.getItemMeta();
        meta.setDisplayName(Chat.colorize(borderName));

        // Đọc lore từ cấu hình
        if (config.contains("items.border.lore")) {
            List<String> lore = new ArrayList<>();
            for (String line : config.getStringList("items.border.lore")) {
                lore.add(Chat.colorize(line));
            }
            meta.setLore(lore);
        }

        borderItem.setItemMeta(meta);

        // Áp dụng các thuộc tính chung từ cấu hình
        borderItem = applyItemProperties(borderItem, "items.border");

        // Đặt viền theo slot được cấu hình
        String[] slots = borderSlots.split(",");
        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, borderItem);
                }
            } catch (NumberFormatException e) {
                // Bỏ qua slot không hợp lệ
            }
        }
    }

    /**
     * Tạo ItemStack từ cấu hình với hỗ trợ tương thích phiên bản và custom model data
     * @param configPath Đường dẫn cấu hình
     * @param material Vật liệu chính
     * @return ItemStack được tạo
     */
    private ItemStack createItemFromConfig(String configPath, String material) {
        return createItemFromConfig(configPath, material, null);
    }

    /**
     * Tạo ItemStack từ cấu hình với hỗ trợ tương thích phiên bản và custom model data
     * @param configPath Đường dẫn cấu hình
     * @param material Vật liệu chính
     * @param materialLegacy Vật liệu cho phiên bản cũ (không sử dụng nữa)
     * @return ItemStack được tạo
     */
    private ItemStack createItemFromConfig(String configPath, String material, String materialLegacy) {
        try {
            ItemStack itemStack;

            // Tạo ItemStack từ material với xử lý tương thích tốt hơn
            itemStack = createItemStackFromMaterial(material);

            // Áp dụng custom model data nếu có và phiên bản hỗ trợ (1.14+)
            FileConfiguration config = getGUIConfig();
            if (config.contains(configPath + ".custom-model-data") && !MaterialCompatibility.isPre113()) {
                int customModelData = config.getInt(configPath + ".custom-model-data", -1);
                if (customModelData > 0) {
                    try {
                        // Sử dụng AdvancedCompatibility để set custom model data an toàn
                        ItemMeta meta = itemStack.getItemMeta();
                        if (meta != null) {
                            AdvancedCompatibility.setCustomModelData(meta, customModelData);
                            itemStack.setItemMeta(meta);

                            if (Storage.getStorage().isDebug()) {
                                Storage.getStorage().getLogger().info("Đã áp dụng CustomModelData " + customModelData + " cho " + configPath);
                            }
                        }
                    } catch (Exception e) {
                        // Phiên bản không hỗ trợ CustomModelData, bỏ qua
                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().warning("Phiên bản không hỗ trợ CustomModelData: " + e.getMessage());
                        }
                    }
                }
            }

            return itemStack;
        } catch (Exception e) {
            // Fallback nếu có lỗi
            handleError("Không thể tạo item từ cấu hình " + configPath + ": " + e.getMessage(), false);
            return new ItemStack(Material.STONE);
        }
    }

    /**
     * Tạo ItemStack từ material string với xử lý tương thích tốt hơn
     * @param materialConfig Cấu hình material
     * @return ItemStack được tạo
     */
    private ItemStack createItemStackFromMaterial(String materialConfig) {
        // Sử dụng function từ MaterialCompatibility
        return MaterialCompatibility.createCompatibleItemStack(materialConfig);
    }





    /**
     * Tạo item trang trí tương thích với cache
     * @param configPath Đường dẫn cấu hình
     * @return ItemStack trang trí
     */
    private ItemStack createCompatibleDecorativeItem(String configPath) {
        // Kiểm tra cache trước
        String cacheKey = configPath + "_decorative";
        if (decorativeItemCache.containsKey(cacheKey)) {
            Long timestamp = cacheTimestamps.get(cacheKey);
            if (timestamp != null && (System.currentTimeMillis() - timestamp) < CACHE_DURATION) {
                return decorativeItemCache.get(cacheKey).clone();
            }
        }

        try {
            FileConfiguration config = getGUIConfig();
            String materialName = config.getString(configPath + ".material", "GRAY_STAINED_GLASS_PANE");
            String displayName = config.getString(configPath + ".name", "&7 ");

            // Sử dụng function tương thích mới
            ItemStack borderItem = createItemStackFromMaterial(materialName);

            ItemMeta meta = borderItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(Chat.colorize(displayName));

                // Áp dụng custom model data
                applyCustomModelData(meta, configPath + ".custom-model-data");

                borderItem.setItemMeta(meta);
            }

            // Lưu vào cache
            decorativeItemCache.put(cacheKey, borderItem.clone());
            cacheTimestamps.put(cacheKey, System.currentTimeMillis());

            return borderItem;
        } catch (Exception e) {
            handleError("Lỗi khi tạo decorative item: " + e.getMessage(), false);
            return new ItemStack(Material.STONE);
        }
    }

    /**
     * Áp dụng custom model data cho ItemMeta nếu phiên bản hỗ trợ
     * @param meta ItemMeta cần áp dụng
     * @param configPath Đường dẫn config cho custom model data
     */
    private void applyCustomModelData(ItemMeta meta, String configPath) {
        if (meta == null) return;

        try {
            // Kiểm tra phiên bản có hỗ trợ Custom Model Data không (1.14+)
            if (MaterialCompatibility.isPre113()) {
                return; // Không hỗ trợ trong phiên bản cũ
            }

            FileConfiguration config = getGUIConfig();
            if (config.contains(configPath)) {
                int customModelData = config.getInt(configPath, -1);
                if (customModelData > 0) {
                    // Sử dụng AdvancedCompatibility để set custom model data an toàn
                    AdvancedCompatibility.setCustomModelData(meta, customModelData);

                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đã áp dụng CustomModelData " + customModelData + " cho " + configPath);
                    }
                }
            }
        } catch (Exception e) {
            handleError("Lỗi khi áp dụng CustomModelData: " + e.getMessage(), false);
        }
    }

    /**
     * Áp dụng các thuộc tính chung cho ItemStack từ cấu hình
     * @param itemStack ItemStack cần áp dụng thuộc tính
     * @param configPath Đường dẫn cấu hình
     * @return ItemStack đã được áp dụng thuộc tính
     */
    private ItemStack applyItemProperties(ItemStack itemStack, String configPath) {
        FileConfiguration config = getGUIConfig();
        ItemMeta meta = itemStack.getItemMeta();
        if (meta == null) return itemStack;

        // Áp dụng unbreakable nếu có và phiên bản hỗ trợ
        if (config.getBoolean(configPath + ".unbreakable", false) && !MaterialCompatibility.isPre113()) {
            meta.setUnbreakable(true);
        }

        // Áp dụng enchants nếu có
        if (config.contains(configPath + ".enchants")) {
            for (String enchantName : config.getConfigurationSection(configPath + ".enchants").getKeys(false)) {
                try {
                    org.bukkit.enchantments.Enchantment enchant = org.bukkit.enchantments.Enchantment.getByName(enchantName);
                    if (enchant != null) {
                        int level = config.getInt(configPath + ".enchants." + enchantName, 1);
                        meta.addEnchant(enchant, level, true);
                    }
                } catch (Exception e) {
                    // Bỏ qua enchant không hợp lệ
                }
            }
        }

        // Áp dụng item flags nếu có
        if (config.contains(configPath + ".flags")) {
            if (config.getBoolean(configPath + ".flags.ALL", false)) {
                // Thêm tất cả flags
                for (org.bukkit.inventory.ItemFlag flag : org.bukkit.inventory.ItemFlag.values()) {
                    meta.addItemFlags(flag);
                }
            } else {
                // Thêm từng flag cụ thể
                for (String flagName : config.getConfigurationSection(configPath + ".flags").getKeys(false)) {
                    if (config.getBoolean(configPath + ".flags." + flagName, false)) {
                        try {
                            org.bukkit.inventory.ItemFlag flag = org.bukkit.inventory.ItemFlag.valueOf(flagName);
                            meta.addItemFlags(flag);
                        } catch (Exception e) {
                            // Bỏ qua flag không hợp lệ
                        }
                    }
                }
            }
        }

        itemStack.setItemMeta(meta);
        return itemStack;
    }

    /**
     * Tính toán vị trí slot cho người chơi dựa trên index
     * @param index Index của người chơi trong danh sách hiển thị
     * @return Slot tương ứng trong inventory
     */
    private int calculatePlayerSlot(int index) {
        // Tính toán số slot trên mỗi hàng (bỏ qua viền trái phải)
        int slotsPerRow = 7;  // 9 - 2 = 7 (bỏ viền trái và phải)
        
        int row = index / slotsPerRow;  // Xác định hàng
        int col = index % slotsPerRow;  // Xác định cột (0-6)
        
        // Tính slot thực tế - BẮT ĐẦU TỪ HÀNG THỨ HAI (row + 1)
        // (row + 1) * 9: Đi đến hàng thứ 2 trở đi
        // col + 1: Cột 0-6 -> Slot 1-7 (bỏ qua viền trái)
        return (row + 1) * 9 + col + 1;
    }
    
    /**
     * Kiểm tra slot có phải là slot hợp lệ cho danh sách người chơi
     * @param slot Slot cần kiểm tra
     * @return true nếu slot nằm trong khu vực danh sách người chơi
     */
    private boolean isValidPlayerSlot(int slot) {
        // Vị trí nằm trong khu vực 4 hàng x 7 cột ở giữa GUI (bỏ qua hàng đầu tiên và viền)
        int row = slot / 9;
        int col = slot % 9;
        
        // Kiểm tra row từ 1-4 (bỏ qua hàng viền trên cùng) và col từ 1-7 (bỏ viền)
        return row >= 1 && row <= 4 && col >= 1 && col <= 7;
    }
    
    /**
     * Xóa khu vực danh sách người chơi trước khi thêm mới
     */
    private void clearPlayerListArea() {
        // Xóa tất cả các slot trong khu vực hiển thị danh sách người chơi
        // (slots 10-16, 19-25, 28-34, 37-43) - bắt đầu từ hàng thứ 2
        for (int row = 1; row < 5; row++) {
            for (int col = 1; col < 8; col++) {
                int slot = row * 9 + col;
                
                // Xóa slot hiện tại
                inventory.setItem(slot, null);
            }
        }
    }
    
    /**
     * Thêm danh sách người chơi
     */
    private void addPlayersList() {
        // Xóa các vật phẩm hiện có trong khu vực danh sách người chơi
        clearPlayerListArea();
        
        List<Player> filteredPlayers = getFilteredPlayers();
        
        // Tính toán vị trí bắt đầu cho trang hiện tại
        int startIndex = page * getPlayersPerPage();
        
        // Hiển thị hướng dẫn nếu không có người chơi nào từ cấu hình
        if (filteredPlayers.isEmpty()) {
            FileConfiguration config = getGUIConfig();

            // Đọc cấu hình cho item "không tìm thấy người chơi"
            String material = config.getString("items.no_player_found.material", "BARRIER");
            int slot = config.getInt("items.no_player_found.slot", 22);

            ItemStack noPlayerItem = createItemFromConfig("items.no_player_found", material);
            ItemMeta meta = noPlayerItem.getItemMeta();

            // Đọc tên hiển thị từ cấu hình
            String displayName = config.getString("items.no_player_found.name", "&cKhông tìm thấy người chơi nào");
            meta.setDisplayName(Chat.colorize(displayName));

            // Đọc lore từ cấu hình
            if (config.contains("items.no_player_found.lore")) {
                List<String> lore = new ArrayList<>();
                for (String line : config.getStringList("items.no_player_found.lore")) {
                    lore.add(Chat.colorize(line));
                }
                meta.setLore(lore);
            }

            noPlayerItem.setItemMeta(meta);

            // Áp dụng các thuộc tính chung từ cấu hình
            noPlayerItem = applyItemProperties(noPlayerItem, "items.no_player_found");

            // Đặt vào vị trí được cấu hình
            inventory.setItem(slot, noPlayerItem);
            return;
        }
        
        // Số người chơi tối đa trên một trang - đọc từ config
        int maxPlayersPerPage = getPlayersPerPage();
        // Giới hạn tối đa 28 người chơi (4 hàng x 7 cột) để phù hợp với GUI layout
        maxPlayersPerPage = Math.min(maxPlayersPerPage, 28);

        // Hiển thị người chơi cho trang hiện tại
        int displayedCount = 0;

        // Debug thông tin về danh sách người chơi
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Hiển thị danh sách người chơi từ " + startIndex + " đến " +
                Math.min(startIndex + maxPlayersPerPage, filteredPlayers.size()) + " (Tổng: " + filteredPlayers.size() +
                ", Config: " + getPlayersPerPage() + ", Thực tế: " + maxPlayersPerPage + ")");
        }

        for (int i = startIndex; i < filteredPlayers.size() && i < startIndex + maxPlayersPerPage && displayedCount < maxPlayersPerPage; i++) {
            Player target = filteredPlayers.get(i);
            
            // Tính toán vị trí slot dựa trên vị trí trong trang hiện tại
            int slot = calculatePlayerSlot(displayedCount);
            
            // Debug thông tin về vị trí slot tính toán
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Player " + target.getName() + " - index: " + displayedCount + 
                    " - slot: " + slot + " - valid: " + isValidPlayerSlot(slot));
            }
            
            // Chỉ đặt item nếu slot nằm trong khu vực danh sách người chơi
            if (isValidPlayerSlot(slot)) {
            // Thêm vào inventory nếu slot hợp lệ
            inventory.setItem(slot, createPlayerHead(target));
            
            // Tăng số người chơi đã hiển thị
            displayedCount++;
            } else {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Slot " + slot + " không hợp lệ cho player " + target.getName());
                }
            }
        }
    }

    /**
     * Tạo item đại diện cho người chơi từ cấu hình
     * @param player Người chơi
     * @return ItemStack đại diện cho người chơi
     */
    private ItemStack createPlayerHead(Player player) {
        // Kiểm tra cache trước khi tạo mới ItemStack
        String cacheKey = "player_" + player.getName().toLowerCase();
        if (playerHeadCache.containsKey(cacheKey)) {
            return playerHeadCache.get(cacheKey);
        }

        FileConfiguration config = getGUIConfig();

        try {
            // Đọc cấu hình cho player head
            String material = config.getString("items.player_list_area.player_head.material", "PLAYER_HEAD");

            // Tạo ItemStack từ cấu hình
            ItemStack playerItem = createItemFromConfig("items.player_list_area.player_head", material);

            // Sử dụng SkullMeta API nếu là đầu người chơi
            ItemMeta meta = playerItem.getItemMeta();
            if (meta instanceof org.bukkit.inventory.meta.SkullMeta) {
                org.bukkit.inventory.meta.SkullMeta skullMeta = (org.bukkit.inventory.meta.SkullMeta) meta;
                skullMeta.setOwner(player.getName());
            }

            // Đọc tên hiển thị từ cấu hình và thay thế placeholder
            String displayName = config.getString("items.player_list_area.player_head.name", "&a&l{player_name}");
            displayName = displayName.replace("{player_name}", player.getName());
            meta.setDisplayName(Chat.colorize(displayName));

            // Đọc lore từ cấu hình và thay thế placeholder
            List<String> lore = new ArrayList<>();
            for (String line : config.getStringList("items.player_list_area.player_head.lore")) {
                String processedLine = line.replace("{player_name}", player.getName());
                lore.add(Chat.colorize(processedLine));
            }
            meta.setLore(lore);

            playerItem.setItemMeta(meta);

            // Áp dụng các thuộc tính chung từ cấu hình
            playerItem = applyItemProperties(playerItem, "items.player_list_area.player_head");

            // Lưu vào cache để tái sử dụng
            playerHeadCache.put(cacheKey, playerItem);

            return playerItem;
        } catch (Exception e) {
            // Log lỗi để debug
            Storage.getStorage().getLogger().warning("Không thể tạo đầu người chơi cho " + player.getName() + ": " + e.getMessage());

            // Fallback nếu có lỗi - sử dụng PAPER
            ItemStack fallbackItem = new ItemStack(Material.PAPER);
            ItemMeta meta = fallbackItem.getItemMeta();
            meta.setDisplayName(Chat.colorize("&a&l" + player.getName()));

            List<String> lore = new ArrayList<>();
            lore.add(Chat.colorize("&7Tên: &a" + player.getName()));
            lore.add(Chat.colorize("&8id:" + player.getName())); // Thêm tên thực
            lore.add("");
            lore.add(Chat.colorize("&eNhấp để chọn người chơi này"));

            meta.setLore(lore);
            fallbackItem.setItemMeta(meta);

            // Lưu vào cache để tái sử dụng
            playerHeadCache.put(cacheKey, fallbackItem);

            return fallbackItem;
        }
    }

    /**
     * Xử lý sự kiện khi người chơi chọn một người chơi khác từ danh sách
     * @param sender Người gửi
     * @param target Người nhận
     */
    private void handlePlayerSelect(Player sender, Player target) {
        // Đóng inventory hiện tại trước để tránh lỗi GUI chồng lên nhau
        sender.closeInventory();

        // Phát âm thanh xác nhận từ cấu hình
        playConfiguredSound(sender, "select_player");

        // Chạy task mở GUI mới sau 1 tick để đảm bảo inventory trước đã đóng hoàn toàn
        Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
            // Mở PlayerActionGUI, một class GUI mới kế thừa IGUI và quản lý Listener riêng
            sender.openInventory(new PlayerActionGUI(sender, target).getInventory());
        }, 1L);
    }

    /**
     * Thêm nút phân trang từ cấu hình
     */
    private void addPaginationButtons() {
        FileConfiguration config = getGUIConfig();
        int totalPages = (int) Math.ceil((double) getFilteredPlayers().size() / getItemsPerPage());

        if (totalPages > 1) {
            // Nút trang trước
            if (page > 0) {
                int slot = config.getInt("items.previous_page.slot", 47);
                String material = config.getString("items.previous_page.material", "ARROW");

                ItemStack prevPageItem = createItemFromConfig("items.previous_page", material);
                ItemMeta meta = prevPageItem.getItemMeta();

                // Đọc tên và thay thế placeholder
                String displayName = config.getString("items.previous_page.name", "&c&l◀ Trang trước");
                meta.setDisplayName(Chat.colorize(displayName));

                // Đọc lore và thay thế placeholder
                List<String> lore = new ArrayList<>();
                for (String line : config.getStringList("items.previous_page.lore")) {
                    String processedLine = line.replace("{current_page}", String.valueOf(page + 1));
                    lore.add(Chat.colorize(processedLine));
                }
                meta.setLore(lore);

                prevPageItem.setItemMeta(meta);

                // Áp dụng các thuộc tính chung từ cấu hình
                prevPageItem = applyItemProperties(prevPageItem, "items.previous_page");

                inventory.setItem(slot, prevPageItem);
            }

            // Nút trang tiếp theo
            if (page < totalPages - 1) {
                int slot = config.getInt("items.next_page.slot", 51);
                String material = config.getString("items.next_page.material", "ARROW");

                ItemStack nextPageItem = createItemFromConfig("items.next_page", material);
                ItemMeta meta = nextPageItem.getItemMeta();

                // Đọc tên và thay thế placeholder
                String displayName = config.getString("items.next_page.name", "&a&l▶ Trang tiếp theo");
                meta.setDisplayName(Chat.colorize(displayName));

                // Đọc lore và thay thế placeholder
                List<String> lore = new ArrayList<>();
                for (String line : config.getStringList("items.next_page.lore")) {
                    String processedLine = line.replace("{current_page}", String.valueOf(page + 1));
                    lore.add(Chat.colorize(processedLine));
                }
                meta.setLore(lore);

                nextPageItem.setItemMeta(meta);

                // Áp dụng các thuộc tính chung từ cấu hình
                nextPageItem = applyItemProperties(nextPageItem, "items.next_page");

                inventory.setItem(slot, nextPageItem);
            }
        }
    }
        
    /**
     * Thêm các nút chức năng từ cấu hình
     */
    private void addFunctionButtons() {
        FileConfiguration config = getGUIConfig();

        // Nút tìm kiếm
        addSearchButton(config);

        // Nút làm mới
        addRefreshButton(config);

        // Nút xóa tìm kiếm
        addClearSearchButton(config);
    }

    /**
     * Thêm nút tìm kiếm từ cấu hình
     */
    private void addSearchButton(FileConfiguration config) {
        int slot = config.getInt("items.search_button.slot", 48);
        String material = config.getString("items.search_button.material", "OAK_SIGN");

        ItemStack searchItem = createItemFromConfig("items.search_button", material);
        ItemMeta meta = searchItem.getItemMeta();

        // Đọc tên và thay thế placeholder
        String displayName = config.getString("items.search_button.name", "&b&l🔍 Tìm kiếm");
        meta.setDisplayName(Chat.colorize(displayName));

        // Đọc lore và thay thế placeholder
        List<String> lore = new ArrayList<>();
        for (String line : config.getStringList("items.search_button.lore")) {
            String processedLine = line.replace("{search_text}",
                searchText != null && !searchText.isEmpty() ? searchText : "Trống");
            lore.add(Chat.colorize(processedLine));
        }
        meta.setLore(lore);

        searchItem.setItemMeta(meta);

        // Áp dụng các thuộc tính chung từ cấu hình
        searchItem = applyItemProperties(searchItem, "items.search_button");

        inventory.setItem(slot, searchItem);
    }

    /**
     * Thêm nút làm mới từ cấu hình
     */
    private void addRefreshButton(FileConfiguration config) {
        int slot = config.getInt("items.refresh_button.slot", 49);
        String material = config.getString("items.refresh_button.material", "SUNFLOWER");

        ItemStack refreshItem = createItemFromConfig("items.refresh_button", material);
        ItemMeta meta = refreshItem.getItemMeta();

        // Đọc tên và thay thế placeholder
        String displayName = config.getString("items.refresh_button.name", "&e&l⟲ Làm mới");
        meta.setDisplayName(Chat.colorize(displayName));

        // Đọc lore và thay thế placeholder
        List<String> lore = new ArrayList<>();
        long cooldownTime = getRefreshCooldown() / 1000; // Chuyển từ ms sang giây
        for (String line : config.getStringList("items.refresh_button.lore")) {
            String processedLine = line.replace("{cooldown_time}", String.valueOf(cooldownTime));
            lore.add(Chat.colorize(processedLine));
        }
        meta.setLore(lore);

        refreshItem.setItemMeta(meta);

        // Áp dụng các thuộc tính chung từ cấu hình
        refreshItem = applyItemProperties(refreshItem, "items.refresh_button");

        inventory.setItem(slot, refreshItem);
    }

    /**
     * Thêm nút xóa tìm kiếm từ cấu hình
     */
    private void addClearSearchButton(FileConfiguration config) {
        // Chỉ hiển thị nút xóa tìm kiếm khi có từ khóa tìm kiếm
        if (searchText == null || searchText.isEmpty()) {
            return;
        }

        int slot = config.getInt("items.clear_search.slot", 50);
        String material = config.getString("items.clear_search.material", "REDSTONE");

        ItemStack clearItem = createItemFromConfig("items.clear_search", material);
        ItemMeta meta = clearItem.getItemMeta();

        // Đọc tên và thay thế placeholder
        String displayName = config.getString("items.clear_search.name", "&c&l✕ Xóa tìm kiếm");
        meta.setDisplayName(Chat.colorize(displayName));

        // Đọc lore và thay thế placeholder
        List<String> lore = new ArrayList<>();
        for (String line : config.getStringList("items.clear_search.lore")) {
            String processedLine = line.replace("{search_text}", searchText);
            lore.add(Chat.colorize(processedLine));
        }
        meta.setLore(lore);

        clearItem.setItemMeta(meta);

        // Áp dụng các thuộc tính chung từ cấu hình
        clearItem = applyItemProperties(clearItem, "items.clear_search");

        inventory.setItem(slot, clearItem);
    }
    
    /**
     * Lấy danh sách người chơi đã lọc và sắp xếp
     * @return Danh sách người chơi đã lọc
     */
    private List<Player> getFilteredPlayers() {
        // Thu thập tất cả người chơi đang online và lọc ngay từ đầu để cải thiện hiệu suất
        List<Player> allPlayers = new ArrayList<>();
        for (Player player : Bukkit.getServer().getOnlinePlayers()) {
            // Chỉ thêm vào danh sách nếu người chơi thật sự online
            if (player != null && player.isOnline()) {
                allPlayers.add(player);
            }
        }
        
        // Tạo bộ lọc để cải thiện hiệu suất
        List<Player> filteredList = new ArrayList<>();
        
        // Xử lý tìm kiếm offline một lần nếu có *
        boolean isExactSearch = false;
        String exactSearchTerm = "";
        if (searchText != null && !searchText.isEmpty() && searchText.startsWith("*")) {
            isExactSearch = true;
            exactSearchTerm = searchText.substring(1).toLowerCase();
        }
        
        // Lọc người chơi với hiệu suất tốt hơn
        for (Player player : allPlayers) {
            // Bỏ qua chính người gửi
            if (player.equals(sender)) {
                continue;
            }
            
            // Kiểm tra người chơi đang ẩn
            if (player.hasMetadata("vanished") || isVanished(player)) {
                if (!sender.hasPermission("storage.admin") && !sender.hasPermission("storage.see_vanished")) {
                    continue;
                }
            }
            
            // Lọc theo từ khóa tìm kiếm
            if (searchText != null && !searchText.isEmpty()) {
                String playerName = player.getName().toLowerCase();
                
                // Tìm kiếm chính xác
                if (isExactSearch) {
                    if (!playerName.equals(exactSearchTerm)) {
                        continue;
                    }
                } 
                // Tìm kiếm thông thường (chứa từ khóa)
                else if (!playerName.contains(searchText.toLowerCase())) {
                    continue;
                }
            }
            
            // Người chơi vượt qua tất cả điều kiện lọc
            filteredList.add(player);
            
            // Kiểm tra số lượng tối đa
            if (filteredList.size() >= MAX_PLAYER_SEARCH_RESULTS) {
                break;
            }
        }
        
        // Sắp xếp danh sách theo VIP và tên
        // Sử dụng cách sắp xếp truyền thống thay vì lambda để tương thích 1.12.2
        Collections.sort(filteredList, new Comparator<Player>() {
            @Override
            public int compare(Player p1, Player p2) {
                // 1. Ưu tiên VIP
                boolean p1Vip = p1.hasPermission("storage.vip");
                boolean p2Vip = p2.hasPermission("storage.vip");
                
                if (p1Vip && !p2Vip) {
                    return -1; // p1 là VIP, p2 không - p1 được xếp trước
                } else if (!p1Vip && p2Vip) {
                    return 1;  // p2 là VIP, p1 không - p2 được xếp trước
                }
                
                // 2. Nếu cả hai đều VIP hoặc không VIP, sắp xếp theo bảng chữ cái
                return p1.getName().compareToIgnoreCase(p2.getName());
            }
        });
        
        return filteredList;
    }
    
    /**
     * Kiểm tra xem người chơi có đang ẩn không
     * @param player Người chơi cần kiểm tra
     * @return true nếu người chơi đang ẩn
     */
    private boolean isVanished(Player player) {
        try {
            // Kiểm tra thông qua metadata
            if (player.hasMetadata("vanished")) {
                for (MetadataValue meta : player.getMetadata("vanished")) {
                    if (meta.asBoolean()) return true;
                }
            }
            
            // Kiểm tra metadata từ các plugin phổ biến
            String[] vanishKeys = {"vanished", "isVanished", "invisible"};
            for (String key : vanishKeys) {
                if (player.hasMetadata(key)) {
                    for (MetadataValue meta : player.getMetadata(key)) {
                        if (meta.asBoolean()) return true;
                    }
                }
            }
            
            // Kiểm tra qua phương thức reflection nếu cần
            if (Bukkit.getPluginManager().isPluginEnabled("Essentials")) {
                try {
                    Object ess = Bukkit.getPluginManager().getPlugin("Essentials");
                    if (ess != null) {
                        // Kiểm tra qua reflection thay vì phụ thuộc trực tiếp
                        Class<?> essClass = ess.getClass();
                        Object essUser = essClass.getMethod("getUser", Player.class).invoke(ess, player);
                        return (boolean) essUser.getClass().getMethod("isVanished").invoke(essUser);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
        }
        return false;
    }
    
    /**
     * Xử lý sự kiện click vào inventory
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent e) {
        // Kiểm tra nếu inventory được click là của GUI này
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(e);
        if (topInventory == null || !topInventory.equals(inventory)) {
            return;
        }
        
        // Luôn hủy tất cả sự kiện click để ngăn người chơi lấy item
        e.setCancelled(true);
        
        // Cập nhật inventory ngay lập tức để đảm bảo thay đổi được áp dụng
        if (e.getWhoClicked() instanceof Player) {
            ((Player) e.getWhoClicked()).updateInventory();
        }
        
        // Ngừng xử lý nếu người chơi không phải là Player
        if (!(e.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player clicker = (Player) e.getWhoClicked();
        
        // Nếu click vào inventory khác với inventory của GUI này, chỉ cần hủy sự kiện
        if (e.getClickedInventory() == null || !e.getClickedInventory().equals(inventory)) {
            return;
        }
        
        int slot = e.getRawSlot();
        
        // Nếu không có item, bỏ qua
        if (e.getCurrentItem() == null || e.getCurrentItem().getType() == Material.AIR) {
            return;
        }

        // Kiểm tra nếu slot thuộc về viền hoặc item trang trí
        if (isDecorationSlot(slot)) {
            // Đơn giản chỉ hủy sự kiện và không làm gì cả
            return;
        }
        
        // Xử lý click vào đầu người chơi (slot < 45 và không phải slot trang trí)
        if (slot < 45 && isValidPlayerSlot(slot)) {
            handlePlayerHeadClick(clicker, slot);
        }
        // Xử lý click vào các nút chức năng dựa trên cấu hình
        else {
            handleFunctionButtonClick(clicker, slot, e.getClick());
        }
    }

    /**
     * Xử lý click vào các nút chức năng dựa trên cấu hình
     */
    private void handleFunctionButtonClick(Player clicker, int slot, ClickType clickType) {
        FileConfiguration config = getGUIConfig();

        // Nút tìm kiếm
        int searchSlot = config.getInt("items.search_button.slot", 48);
        if (slot == searchSlot) {
            handleSearchButtonClick(clicker, clickType);
            return;
        }

        // Nút trang trước
        int prevSlot = config.getInt("items.previous_page.slot", 47);
        if (slot == prevSlot && page > 0) {
            PlayerSearchGUI newGUI = new PlayerSearchGUI(clicker, searchText, page - 1);
            clicker.openInventory(newGUI.getInventory());
            playConfiguredSound(clicker, "page_change");
            return;
        }

        // Nút trang sau
        int nextSlot = config.getInt("items.next_page.slot", 51);
        if (slot == nextSlot) {
            List<Player> filteredPlayers = getFilteredPlayers();
            int totalPages = (int) Math.ceil((double) filteredPlayers.size() / getItemsPerPage());
            if (page < totalPages - 1) {
                PlayerSearchGUI newGUI = new PlayerSearchGUI(clicker, searchText, page + 1);
                clicker.openInventory(newGUI.getInventory());
                playConfiguredSound(clicker, "page_change");
            }
            return;
        }

        // Nút làm mới
        int refreshSlot = config.getInt("items.refresh_button.slot", 49);
        if (slot == refreshSlot) {
            handleRefreshButtonClick(clicker);
            return;
        }

        // Nút xóa tìm kiếm
        int clearSlot = config.getInt("items.clear_search.slot", 50);
        if (slot == clearSlot) {
            handleClearSearchClick(clicker);
            return;
        }
    }

    /**
     * Phát âm thanh từ cấu hình
     */
    private void playConfiguredSound(Player player, String soundKey) {
        try {
            FileConfiguration config = getGUIConfig();
            // Đọc âm thanh từ cấp độ gốc thay vì từ section sounds
            String soundConfig = config.getString(soundKey + "_sound", "");

            // Fallback cho cấu trúc cũ nếu không tìm thấy
            if (soundConfig.isEmpty()) {
                soundConfig = config.getString("sounds." + soundKey, "");
            }

            if (!soundConfig.isEmpty()) {
                SoundManager.playSoundFromConfig(player, soundConfig);
            }
        } catch (Exception e) {
            // Fallback âm thanh mặc định sử dụng SoundCompatibility
            com.hongminh54.storage.Utils.SoundCompatibility.playSound(player, "UI_BUTTON_CLICK", 0.5f, 1.0f);
        }
    }

    /**
     * Xử lý click vào nút xóa tìm kiếm
     */
    private void handleClearSearchClick(Player clicker) {
        // Xóa từ khóa tìm kiếm và mở lại GUI
        PlayerSearchGUI newGUI = new PlayerSearchGUI(clicker, "", 0);
        clicker.openInventory(newGUI.getInventory());

        // Phát âm thanh và thông báo
        playConfiguredSound(clicker, "search");

        // Gửi thông báo từ message.yml
        FileConfiguration messageConfig = File.getMessage();
        String message = messageConfig.getString("user.player_search.search_cleared", "&8[&a&l✓&8] &aĐã xóa từ khóa tìm kiếm!");
        clicker.sendMessage(Chat.colorize(message));
    }

    /**
     * Kiểm tra xem một slot có phải là slot trang trí không
     * @param slot Slot cần kiểm tra
     * @return true nếu slot là slot trang trí
     */
    private boolean isDecorationSlot(int slot) {
        FileConfiguration config = getGUIConfig();

        // Kiểm tra slot viền từ cấu hình
        String borderSlots = config.getString("items.border.slot", "0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 52, 53");
        String[] slots = borderSlots.split(",");
        for (String slotStr : slots) {
            try {
                int borderSlot = Integer.parseInt(slotStr.trim());
                if (slot == borderSlot) {
                    return true;
                }
            } catch (NumberFormatException e) {
                // Bỏ qua slot không hợp lệ
            }
        }

        // Kiểm tra slot "không tìm thấy người chơi"
        int noPlayerSlot = config.getInt("items.no_player_found.slot", 22);
        if (slot == noPlayerSlot) {
            return true;
        }

        // Kiểm tra xem slot có phải là slot đầu người chơi hợp lệ không
        if (slot < 45) {
            return !isValidPlayerSlot(slot);
        }

        return false;
    }
    
    /**
     * Xử lý khi click vào đầu người chơi
     */
    private void handlePlayerHeadClick(Player clicker, int slot) {
        ItemStack item = inventory.getItem(slot);
        if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            String displayName = item.getItemMeta().getDisplayName();
            String playerName = null;
            
            // Tìm kiếm tên trong lore (format: "&8id:PlayerName")
            if (item.getItemMeta().hasLore()) {
                List<String> lore = item.getItemMeta().getLore();
                for (String line : lore) {
                    if (line == null) continue; // Kiểm tra null để tránh NullPointerException
                    
                    String stripped = Chat.stripColor(line);
                    if (stripped.startsWith("id:")) {
                        playerName = stripped.substring(3); // Lấy phần sau "id:"
                        break;
                    }
                }
            }
            
            // Nếu không tìm thấy trong lore, thử qua displayName
            if (playerName == null || playerName.isEmpty()) {
                // Loại bỏ mã màu để lấy tên thực của người chơi
                String realPlayerName = Chat.stripColor(displayName);
                
                // Thử tìm người chơi
                Player target = Bukkit.getPlayer(realPlayerName);
                
                // Nếu không tìm thấy, thử lấy ra phần tên từ displayName (cắt bỏ prefix/suffix)
                if (target == null) {
                    // Mẫu thường gặp: "&a&lTên_người_chơi"
                    String[] parts = realPlayerName.split(" ");
                    if (parts.length > 0) {
                        playerName = parts[parts.length - 1];
                    } else {
                        playerName = realPlayerName;
                    }
                } else {
                    playerName = target.getName();
                }
            }
            
            // Tìm người chơi với tên đã xử lý
            if (playerName != null && !playerName.isEmpty()) {
                // Tạo biến final cho playerName để sử dụng trong lambda
                final String finalPlayerName = playerName;
                
                Player target = Bukkit.getPlayer(finalPlayerName);
                
                if (target != null && target.isOnline()) {
                    // Biến final cho target để sử dụng trong lambda
                    final Player finalTarget = target;
                    
                    // Đóng inventory hiện tại trước để tránh lỗi GUI chồng lên nhau
                    clicker.closeInventory();
                    
                    // Chờ một tick để đảm bảo inventory đã đóng hoàn toàn
                    Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                        if (finalTarget.isOnline() && clicker.isOnline()) {
                            handlePlayerSelect(clicker, finalTarget);
                        } else {
                            // Người chơi không còn online
                            if (clicker.isOnline()) {
                                clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + finalPlayerName + " &ckhông còn trực tuyến!"));
                                // Âm thanh thất bại từ cấu hình
                                playConfiguredSound(clicker, "error");
                                // Mở lại GUI tìm kiếm
                                clicker.openInventory(new PlayerSearchGUI(clicker, searchText, page).getInventory());
                            }
                        }
                    }, 2L);
                } else {
                    // Debug log
                    Storage.getStorage().getLogger().info("PlayerSearchGUI: Không tìm thấy người chơi từ tên: " + finalPlayerName);
                    Storage.getStorage().getLogger().info("PlayerSearchGUI: DisplayName gốc: " + displayName);
                    
                    // Người chơi không còn online
                    clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + finalPlayerName + " &ckhông còn trực tuyến!"));
                    // Âm thanh thất bại từ cấu hình
                    playConfiguredSound(clicker, "error");
                    // Làm mới danh sách
                    clicker.closeInventory();
                    
                    // Mở lại GUI với độ trễ để tránh xung đột
                    Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                        if (clicker.isOnline()) {
                            clicker.openInventory(new PlayerSearchGUI(clicker, searchText, page).getInventory());
                        }
                    }, 2L);
                }
            } else {
                // Không thể xác định tên người chơi
                clicker.sendMessage(Chat.colorize("&8[&4&l✕&8] &cKhông thể xác định người chơi từ item."));
                // Âm thanh thất bại từ cấu hình
                playConfiguredSound(clicker, "error");
            }
        }
    }
    
    /**
     * Xử lý khi click vào nút tìm kiếm
     */
    private void handleSearchButtonClick(Player clicker, ClickType clickType) {
        if (clickType.isLeftClick()) {
            // Đóng inventory trước khi mở khung nhập
            clicker.closeInventory();
            
            // Lưu thông tin trang và từ khóa hiện tại
            final String currentSearchText = this.searchText;
            final int currentPage = this.page;
            
            // Chờ trước khi hiển thị khung nhập để đảm bảo inventory đã đóng hoàn toàn
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                if (!clicker.isOnline()) return;
                
                // Hủy tất cả input đang chờ trước khi bắt đầu mới
                PlayerSearchChatHandler.cancelInput(clicker);
                
                // Phát âm thanh khi mở khung nhập từ cấu hình
                playConfiguredSound(clicker, "search");
                
                // Sử dụng PlayerChatHandler để xử lý nhập liệu
                PlayerSearchChatHandler.startChatInput(
                    clicker, 
                    "&aTìm kiếm người chơi",
                    (playerName) -> {
                        // Kiểm tra người chơi vẫn còn online
                        if (!clicker.isOnline()) return;
                        
                        if (playerName == null || playerName.isEmpty()) {
                            // Nếu nhập rỗng, mở lại giao diện tìm kiếm ban đầu
                            clicker.sendMessage(Chat.colorize("&8[&e&l!&8] &eTìm kiếm đã bị hủy do không nhập tên người chơi"));
                            
                            // Mở lại GUI với từ khóa tìm kiếm cũ (nếu có)
                            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                                if (clicker.isOnline()) {
                                    if (currentSearchText != null && !currentSearchText.isEmpty()) {
                                        clicker.openInventory(new PlayerSearchGUI(clicker, currentSearchText, currentPage).getInventory());
                                    } else {
                                        clicker.openInventory(new PlayerSearchGUI(clicker).getInventory());
                                    }
                                }
                            }, 2L);
                            return;
                        }
                        
                        // Thông báo đang tìm kiếm từ message.yml
                        FileConfiguration messageConfig = File.getMessage();
                        String searchingMessage = messageConfig.getString("user.player_search.searching", "&8[&a&l❖&8] &aĐang tìm kiếm người chơi: &e'{search_text}'");
                        searchingMessage = searchingMessage.replace("{search_text}", playerName);
                        clicker.sendMessage(Chat.colorize("&8&m------------------------------"));
                        clicker.sendMessage(Chat.colorize(searchingMessage));

                        // Mở lại GUI với từ khóa tìm kiếm mới sau 2 tick để đảm bảo mọi thứ đã sẵn sàng
                        final String finalPlayerName = playerName; // Cần biến final cho lambda
                        Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                            if (!clicker.isOnline()) return;

                            // Tạo GUI mới trước khi mở
                            PlayerSearchGUI newGUI = new PlayerSearchGUI(clicker, finalPlayerName, 0);

                            // Đếm kết quả tìm kiếm
                            int resultCount = newGUI.getFilteredPlayers().size();

                            // Mở inventory mới
                            clicker.openInventory(newGUI.getInventory());

                            // Thông báo kết quả tìm kiếm từ message.yml
                            FileConfiguration resultMessageConfig = File.getMessage();
                            if (resultCount > 0) {
                                String resultMessage = resultMessageConfig.getString("user.player_search.search_result", "&8[&a&l✓&8] &aTìm thấy &e{count} &anguời chơi phù hợp!");
                                resultMessage = resultMessage.replace("{count}", String.valueOf(resultCount));
                                clicker.sendMessage(Chat.colorize(resultMessage));
                                // Phát âm thanh xác nhận khi có kết quả
                                playConfiguredSound(clicker, "search");
                            } else {
                                String noResultMessage = resultMessageConfig.getString("user.player_search.no_player_found", "&cKhông tìm thấy người chơi nào phù hợp với từ khóa: &f{search_text}");
                                noResultMessage = noResultMessage.replace("{search_text}", finalPlayerName);
                                clicker.sendMessage(Chat.colorize(noResultMessage));
                                // Phát âm thanh thất bại khi không có kết quả
                                playConfiguredSound(clicker, "error");
                            }
                        }, 2L);
                    }
                );
            }, 2L);
            
        } else if (clickType.isRightClick()) {
            // Kiểm tra nếu đang có từ khóa tìm kiếm
            if (searchText != null && !searchText.isEmpty()) {
                // Xóa từ khóa tìm kiếm và làm mới danh sách
                clicker.closeInventory();
                
                // Phát âm thanh khi làm mới từ cấu hình
                playConfiguredSound(clicker, "search");

                // Thông báo hủy tìm kiếm từ message.yml
                FileConfiguration messageConfig = File.getMessage();
                String message = messageConfig.getString("user.player_search.search_cleared", "&8[&a&l✓&8] &aĐã xóa từ khóa tìm kiếm!");
                clicker.sendMessage(Chat.colorize(message));
                
                // Mở lại GUI không có từ khóa tìm kiếm sau 2 tick để đảm bảo đồng bộ
                Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                    if (clicker.isOnline()) {
                        // Xóa cache trước khi mở lại GUI
                        clearCacheForSearch(searchText);
                        
                        // Mở GUI mới không có từ khóa tìm kiếm
                        clicker.openInventory(new PlayerSearchGUI(clicker).getInventory());
                    }
                }, 2L);
            } else {
                // Nếu không có từ khóa tìm kiếm, chỉ làm mới GUI
                clicker.closeInventory();
                
                // Phát âm thanh từ cấu hình
                playConfiguredSound(clicker, "refresh");
                
                // Mở lại GUI sau 2 tick
                Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                    if (clicker.isOnline()) {
                        clicker.openInventory(new PlayerSearchGUI(clicker).getInventory());
                    }
                }, 2L);
            }
        }
    }
    
    /**
     * Xóa cache liên quan đến từ khóa tìm kiếm
     * @param searchText Từ khóa tìm kiếm
     */
    private void clearCacheForSearch(String searchText) {
        if (searchText == null || searchText.isEmpty()) {
            return;
        }
        
        // Xóa cache liên quan đến từ khóa tìm kiếm
        List<String> keysToRemove = new ArrayList<>();
        
        // Tìm các khóa cache có chứa từ khóa tìm kiếm
        for (String key : playerHeadCache.keySet()) {
            if (key.contains(searchText.toLowerCase())) {
                keysToRemove.add(key);
            }
        }
        
        // Xóa các cache đã tìm thấy
        for (String key : keysToRemove) {
            playerHeadCache.remove(key);
        }
        
        // Xóa cache trong dataCache
        List<String> dataKeysToRemove = new ArrayList<>();
        for (String key : dataCache.keySet()) {
            if (key.contains(searchText.toLowerCase())) {
                dataKeysToRemove.add(key);
            }
        }
        
        // Xóa các cache trong dataCache
        for (String key : dataKeysToRemove) {
            dataCache.remove(key);
        }
    }
    
    /**
     * Xử lý khi click vào nút làm mới
     */
    private void handleRefreshButtonClick(Player clicker) {
        // Kiểm tra cooldown
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRefreshTime < getRefreshCooldown()) {
            // Gửi thông báo từ message.yml
            FileConfiguration messageConfig = File.getMessage();
            long cooldownTime = (getRefreshCooldown() - (currentTime - lastRefreshTime)) / 1000;
            String message = messageConfig.getString("user.player_search.cooldown", "&8[&c&l!&8] &cVui lòng chờ &e{time}s &ctrước khi làm mới lại!");
            message = message.replace("{time}", String.valueOf(cooldownTime));
            clicker.sendMessage(Chat.colorize(message));

            // Phát âm thanh thất bại từ cấu hình
            playConfiguredSound(clicker, "error");
            return;
        }
        
        // Cập nhật thời gian làm mới gần nhất
        lastRefreshTime = currentTime;

        // Làm mới danh sách người chơi
        clicker.closeInventory();
        clicker.openInventory(new PlayerSearchGUI(clicker, searchText, page).getInventory());

        // Phát âm thanh khi làm mới từ cấu hình
        playConfiguredSound(clicker, "refresh");

        // Thông báo từ message.yml
        FileConfiguration messageConfig = File.getMessage();
        String message = messageConfig.getString("user.player_search.refreshed", "&8[&a&l✓&8] &aDanh sách người chơi đã được làm mới!");
        clicker.sendMessage(Chat.colorize(message));
    }
    
    /**
     * Xử lý sự kiện đóng inventory
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().equals(inventory)) {
            // Sử dụng lambda với độ trễ 2 tick
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                // Chỉ hủy đăng ký listener nếu inventory vẫn là của đối tượng này
                if (listenerRegistered) {
                    unregisterListener();
                    
                    // Xóa cache không cần thiết để giảm sử dụng bộ nhớ
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đã xóa cache sau khi đóng GUI PlayerSearchGUI");
                    }
                }
            }, 2L);
        }
    }
    
    /**
     * Xử lý sự kiện kéo thả item trong inventory
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryDrag(InventoryDragEvent e) {
        // Kiểm tra nếu inventory trong view là của GUI này
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(e);
        if (topInventory != null && topInventory.equals(inventory)) {
            // Hủy tất cả các sự kiện kéo thả để ngăn người chơi lấy item
            e.setCancelled(true);
            
            // Cập nhật inventory ngay lập tức để đảm bảo thay đổi được áp dụng
            if (e.getWhoClicked() instanceof Player) {
                ((Player) e.getWhoClicked()).updateInventory();
            }
        }
    }

    /**
     * Xóa cache cho người chơi cụ thể
     * @param player Người chơi cần xóa cache
     */
    public void clearCacheForPlayer(Player player) {
        if (player != null) {
            String cacheKey = "player_" + player.getName().toLowerCase();
            playerHeadCache.remove(cacheKey);
        }
    }
    
    /**
     * Xóa toàn bộ cache đầu người chơi (phương thức tĩnh)
     * Có thể được gọi khi plugin vô hiệu hóa
     */
    public static void clearAllHeadCache() {
        // Sử dụng Bukkit.getOnlinePlayers() để tránh tham chiếu tới instance
        for (Player p : Bukkit.getOnlinePlayers()) {
            try {
                // Đảm bảo mọi GUI được đóng trước khi plugin vô hiệu hóa
                if (p.getOpenInventory() != null) {
                    try {
                        Inventory openTopInventory = p.getOpenInventory().getTopInventory();
                        if (openTopInventory != null && openTopInventory.getHolder() instanceof PlayerSearchGUI) {
                            p.closeInventory();
                        }
                    } catch (Exception ex) {
                        // Bỏ qua lỗi khi lấy top inventory
                    }
                }
            } catch (Exception e) {
                // Bỏ qua lỗi
            }
        }
    }

    /**
     * Kiểm tra và làm sạch cache định kỳ
     */
    public void performCacheMaintenance() {
        try {
            // Xóa cache cũ
            clearExpiredCache();

            // Xóa cache player head cũ
            long currentTime = System.currentTimeMillis();
            playerHeadCache.entrySet().removeIf(entry -> {
                // Cache player head có thời hạn 10 phút
                return (currentTime - lastRefreshTime) > 600000;
            });

            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Đã thực hiện bảo trì cache cho PlayerSearchGUI");
            }
        } catch (Exception e) {
            handleError("Lỗi khi thực hiện bảo trì cache: " + e.getMessage(), false);
        }
    }

    /**
     * Kiểm tra tính hợp lệ của cấu hình GUI
     * @return true nếu cấu hình hợp lệ
     */
    public static boolean validateGUIConfig() {
        try {
            FileConfiguration config = getGUIConfig();
            if (config == null) {
                handleError("Không thể tải cấu hình GUI", true);
                return false;
            }

            // Kiểm tra các trường bắt buộc
            String[] requiredFields = {
                "title", "size", "items.decorates.material",
                "items.player_list_area.player_head.material",
                "items.search_button.material", "items.refresh_button.material"
            };

            for (String field : requiredFields) {
                if (!config.contains(field)) {
                    handleError("Thiếu trường cấu hình bắt buộc: " + field, true);
                    return false;
                }
            }

            // Kiểm tra kích thước GUI hợp lệ
            int size = config.getInt("size", 6);
            if (size < 1 || size > 6) {
                handleError("Kích thước GUI không hợp lệ: " + size + " (phải từ 1-6)", true);
                return false;
            }

            return true;
        } catch (Exception e) {
            handleError("Lỗi khi kiểm tra cấu hình GUI: " + e.getMessage(), true);
            return false;
        }
    }

    /**
     * Tối ưu hóa hiệu suất GUI
     */
    public void optimizePerformance() {
        try {
            // Kiểm tra số lượng player online để tối ưu hiệu suất
            Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
            if (onlinePlayers.size() > 100) {
                handleError("Quá nhiều người chơi online (" + onlinePlayers.size() + "), có thể ảnh hưởng hiệu suất GUI", false);
            }

            // Xóa cache cũ định kỳ
            if ((System.currentTimeMillis() - lastRefreshTime) > 300000) { // 5 phút
                performCacheMaintenance();
            }

        } catch (Exception e) {
            handleError("Lỗi khi tối ưu hiệu suất: " + e.getMessage(), false);
        }
    }
}
