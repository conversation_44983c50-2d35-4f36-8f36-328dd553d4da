package com.hongminh54.storage.GUI;

import java.util.Arrays;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Action.ConvertBlock;
import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.MaterialCompatibility;
import com.hongminh54.storage.Utils.SoundManager;

/**
 * GUI xác nhận chuyển đổi block/phôi
 */
public class ConvertConfirmationGUI implements IGUI {

    private final Player player;
    private final String material;
    private final int amount;
    private final boolean isReverseConversion;
    private final FileConfiguration config;

    public ConvertConfirmationGUI(Player player, String material, int amount, boolean isReverseConversion) {
        this.player = player;
        this.material = material;
        this.amount = amount;
        this.isReverseConversion = isReverseConversion;
        this.config = File.getGUIConfig("convert_confirmation");
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Tạo inventory với kích thước từ config
        int size = config.getInt("size", 3) * 9;
        String title = config.getString("title", "&8Xác nhận chuyển đổi");
        title = title.replace("#player#", player.getName());
        
        Inventory inventory = Bukkit.createInventory(this, size, GUIText.format(title));
        
        // Phát âm thanh khi mở
        try {
            String openSound = config.getString("open_sound", "BLOCK_CHEST_OPEN:1.0:1.0");
            SoundManager.playSoundFromConfig(player, openSound);
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
        
        // Thêm các item trang trí
        addDecorativeItems(inventory);
        
        // Thêm thông tin chuyển đổi
        addConversionInfo(inventory);
        
        // Thêm nút xác nhận và hủy
        addControlButtons(inventory);
        
        return inventory;
    }
    
    /**
     * Thêm các item trang trí
     */
    private void addDecorativeItems(Inventory inventory) {
        // Lấy cấu hình item trang trí
        String decorateSlots = config.getString("items.decorates.slot", "0,1,2,6,7,8,9,17,18,19,20,24,25,26");
        String[] slots = decorateSlots.split(",");
        
        Material decorateMaterial = getCompatibleDecorativeMaterial();
        ItemStack decorateItem = ItemManager.createItem(
            decorateMaterial,
            config.getString("items.decorates.name", "&7 "),
            Arrays.asList(config.getString("items.decorates.lore", "&7 "))
        );
        
        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                if (slot >= 0 && slot < inventory.getSize()) {
                    // Tạo item trang trí không thể tương tác (không có NBT tag)
                    ItemStack safeDecorateItem = decorateItem.clone();
                    inventory.setItem(slot, safeDecorateItem);
                }
            } catch (NumberFormatException e) {
                // Bỏ qua slot không hợp lệ
            }
        }
    }
    
    /**
     * Thêm thông tin chuyển đổi
     */
    private void addConversionInfo(Inventory inventory) {
        String materialName = getDisplayMaterialName(material);
        
        // Lấy số lượng hiện có
        int currentAmount = MineManager.getPlayerBlock(player, material);
        
        // Tính toán số lượng sẽ chuyển đổi
        int convertAmount = (amount > 0) ? amount : currentAmount;
        
        Material displayMaterial = getCompatibleMaterial(material);
        
        String infoTitle;
        String conversionDescription;
        int resultAmount;

        if (isReverseConversion) {
            // Block -> Phôi
            infoTitle = File.getMessage().getString("user.action.convert.confirmation_title", "&eXác nhận chuyển đổi");
            conversionDescription = File.getMessage().getString("user.action.convert.confirmation_block_to_ingot",
                "&aChuyển &f#amount# &ablock &f#material# &athành &f#result# &aphôi");
            resultAmount = convertAmount * 9; // 1 block = 9 phôi
        } else {
            // Phôi -> Block
            infoTitle = File.getMessage().getString("user.action.convert.confirmation_title", "&eXác nhận chuyển đổi");
            conversionDescription = File.getMessage().getString("user.action.convert.confirmation_ingot_to_block",
                "&aChuyển &f#amount# &aphôi &f#material# &athành &f#result# &ablock");
            resultAmount = convertAmount / 9; // 9 phôi = 1 block
        }

        // Thay thế placeholder trong description với kiểm tra null
        conversionDescription = safeReplaceDescription(conversionDescription, convertAmount, materialName, resultAmount);
        
        ItemStack infoItem = ItemManager.createItem(
            displayMaterial,
            infoTitle,
            Arrays.asList(
                "&7",
                "&fVật liệu: &b" + materialName,
                conversionDescription,
                "&fSố lượng hiện có: &e" + currentAmount,
                "&fSố lượng chuyển đổi: &c" + convertAmount,
                "&fKết quả nhận được: &a" + resultAmount,
                "&7",
                "&7Bạn có chắc chắn muốn thực hiện?"
            )
        );
        
        // Đặt item vào slot trung tâm với validation
        int infoSlot = config.getInt("items.conversion_info.slot", 13);
        if (infoSlot < 0 || infoSlot >= inventory.getSize()) {
            infoSlot = 13; // Fallback về slot mặc định
        }
        inventory.setItem(infoSlot, infoItem);
    }
    
    /**
     * Thêm nút xác nhận và hủy
     */
    private void addControlButtons(Inventory inventory) {
        // Nút xác nhận
        String confirmName = File.getMessage().getString("user.action.convert.confirmation_confirm", "&a&lXác nhận");
        String confirmLore = config.getString("items.confirm.lore", "&eClick để xác nhận chuyển đổi");

        ItemStack confirmItem = ItemManager.createItem(
            Material.EMERALD_BLOCK,
            confirmName,
            Arrays.asList(confirmLore)
        );
        
        int confirmSlot = config.getInt("items.confirm.slot", 11);
        // Đảm bảo slot hợp lệ
        if (confirmSlot < 0 || confirmSlot >= inventory.getSize()) {
            confirmSlot = 11; // Fallback về slot mặc định
        }

        InteractiveItem confirmButton = new InteractiveItem(confirmItem, confirmSlot).onClick((p, clickType) -> {
            // Phát âm thanh xác nhận
            try {
                String confirmSound = config.getString("confirm_sound", "BLOCK_ANVIL_USE:1.0:1.2");
                SoundManager.playSoundFromConfig(p, confirmSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
            
            // Thực hiện chuyển đổi
            p.closeInventory();
            new ConvertBlock(p, material, amount, isReverseConversion).doAction();
            
            // Mở lại GUI convert block sau khi hoàn thành
            Bukkit.getScheduler().runTaskLater(com.hongminh54.storage.Storage.getStorage(), () -> {
                if (p.isOnline()) {
                    p.openInventory(new ConvertBlockGUI(p).getInventory());
                }
            }, 2L);
        });
        
        inventory.setItem(confirmButton.getSlot(), confirmButton);
        
        // Nút hủy
        String cancelName = File.getMessage().getString("user.action.convert.confirmation_cancel", "&c&lHủy");
        String cancelLore = config.getString("items.cancel.lore", "&eClick để hủy và quay lại");

        ItemStack cancelItem = ItemManager.createItem(
            Material.BARRIER,
            cancelName,
            Arrays.asList(cancelLore)
        );
        
        int cancelSlot = config.getInt("items.cancel.slot", 15);
        // Đảm bảo slot hợp lệ
        if (cancelSlot < 0 || cancelSlot >= inventory.getSize()) {
            cancelSlot = 15; // Fallback về slot mặc định
        }

        InteractiveItem cancelButton = new InteractiveItem(cancelItem, cancelSlot).onClick((p, clickType) -> {
            // Phát âm thanh hủy
            try {
                String cancelSound = config.getString("cancel_sound", "UI_BUTTON_CLICK:0.5:1.0");
                SoundManager.playSoundFromConfig(p, cancelSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
            
            // Quay lại GUI convert block
            p.closeInventory();
            Bukkit.getScheduler().runTaskLater(com.hongminh54.storage.Storage.getStorage(), () -> {
                if (p.isOnline()) {
                    p.openInventory(new ConvertBlockGUI(p).getInventory());
                }
            }, 1L);
        });
        
        inventory.setItem(cancelButton.getSlot(), cancelButton);
    }

    /**
     * Lấy Material tương thích với phiên bản hiện tại
     * Hỗ trợ format "MATERIAL;DATA" cho 1.12.2 và "MATERIAL" cho 1.13+
     *
     * @param materialString Chuỗi material (có thể có data)
     * @return Material tương thích hoặc STONE nếu không tìm thấy
     */
    private Material getCompatibleMaterial(String materialString) {
        try {
            // Sử dụng MaterialCompatibility để xử lý material an toàn
            Material material = MaterialCompatibility.getMaterialSafely(materialString);
            return material != null ? material : Material.STONE;
        } catch (Exception e) {
            com.hongminh54.storage.Storage.getStorage().getLogger().warning("Không tìm thấy material: " + materialString);
            return Material.STONE; // Fallback
        }
    }

    /**
     * Tạo ItemStack tương thích đa phiên bản
     * Sử dụng getCompatibleMaterial() để lấy Material tương thích
     *
     * @param materialString Chuỗi material (có thể có data cho 1.12.2)
     * @param displayName Tên hiển thị
     * @param lore Danh sách lore
     * @return ItemStack tương thích
     */
    private ItemStack createVersionCompatibleItem(String materialString, String displayName, java.util.List<String> lore) {
        try {
            // Lấy Material tương thích
            Material compatibleMaterial = getCompatibleMaterial(materialString);

            // Tạo ItemStack với Material đã tương thích
            return ItemManager.createItem(compatibleMaterial, displayName, lore);
        } catch (Exception e) {
            // Fallback về item STONE nếu có lỗi
            return ItemManager.createItem(Material.STONE, displayName, lore);
        }
    }

    /**
     * Lấy tên hiển thị của material từ config hoặc fallback
     * Hỗ trợ đa phiên bản và xử lý material có data
     *
     * @param materialString Chuỗi material
     * @return Tên hiển thị của material
     */
    private String getDisplayMaterialName(String materialString) {
        // Thử lấy từ config trước
        String configName = File.getConfig().getString("items." + materialString);
        if (configName != null && !configName.isEmpty()) {
            return configName;
        }

        // Fallback: lấy tên từ material name
        String materialName = materialString.split(";")[0];

        // Chuyển đổi tên material thành dạng dễ đọc
        return materialName.toLowerCase()
                .replace("_", " ")
                .replace("block", "")
                .trim();
    }

    /**
     * Lấy Material trang trí tương thích với phiên bản hiện tại
     * Sử dụng BLACK_STAINED_GLASS_PANE cho 1.13+ và STAINED_GLASS_PANE cho 1.12.2
     *
     * @return Material trang trí tương thích
     */
    private Material getCompatibleDecorativeMaterial() {
        try {
            // Thử BLACK_STAINED_GLASS_PANE trước (1.13+)
            return Material.valueOf("BLACK_STAINED_GLASS_PANE");
        } catch (IllegalArgumentException e) {
            try {
                // Fallback về STAINED_GLASS_PANE (1.12.2)
                return Material.valueOf("STAINED_GLASS_PANE");
            } catch (IllegalArgumentException e2) {
                // Fallback cuối cùng
                return Material.valueOf("GLASS_PANE");
            }
        }
    }

    /**
     * Thay thế placeholder trong description một cách an toàn
     * Kiểm tra null và cung cấp giá trị mặc định
     *
     * @param description Mô tả gốc
     * @param convertAmount Số lượng chuyển đổi
     * @param materialName Tên vật liệu
     * @param resultAmount Số lượng kết quả
     * @return Mô tả đã được thay thế placeholder
     */
    private String safeReplaceDescription(String description, int convertAmount, String materialName, int resultAmount) {
        if (description == null) {
            description = "&aChuyển đổi &f#amount# &a#material# &athành &f#result#";
        }

        if (materialName == null) {
            materialName = "Unknown";
        }

        return description
            .replace("#amount#", String.valueOf(convertAmount))
            .replace("#material#", materialName)
            .replace("#result#", String.valueOf(resultAmount));
    }
}
