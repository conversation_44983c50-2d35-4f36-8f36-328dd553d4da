package com.hongminh54.storage.GUI;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.AdvancedCompatibility;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.InventoryCompatibility;
import com.hongminh54.storage.Utils.MaterialCompatibility;
import com.hongminh54.storage.Utils.PlayerCompatibility;
import com.hongminh54.storage.Utils.SoundManager;
import com.hongminh54.storage.Utils.TransferManager;

/**
 * Giao diện chuyển nhiều loại tài nguyên cùng lúc
 */
public class MultiTransferGUI implements IGUI, Listener {

    private final Player sender;
    private final Player receiver;
    private final Map<String, Integer> transferItems = new HashMap<>();
    private final FileConfiguration config;
    private Inventory inventory;
    private boolean listenerRegistered = false;

    // NMS Assistant cho version checking
    private static final NMSAssistant nmsAssistant = new NMSAssistant();

    private static final int GUI_UPDATE_INTERVAL = 5; // Ticks giữa mỗi lần cập nhật GUI
    private static final int MAX_VISIBLE_RESOURCES = 28; // Số lượng tài nguyên tối đa hiển thị mỗi trang
    private static long lastSoundPlayed = 0; // Thời gian phát âm thanh gần nhất
    private static final long SOUND_COOLDOWN = 100; // Thời gian chờ giữa các lần phát âm thanh (milliseconds)
    
    // Cache các ItemStack để tránh tạo mới liên tục
    private static final Map<String, ItemStack> buttonCache = new HashMap<>();
    
    // Tạo và lưu các nút điều hướng vào cache để tái sử dụng
    private static ItemStack getCachedButton(String key, Material material, String name, String... lore) {
        return buttonCache.computeIfAbsent(key, k -> {
            ItemStack button = new ItemStack(material);
            ItemMeta meta = button.getItemMeta();
            meta.setDisplayName(name);
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore));
            }
            button.setItemMeta(meta);
            return button;
        });
    }
    
    // Phát âm thanh với cooldown
    private void playSound(Player player, Sound sound, float volume, float pitch) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSoundPlayed >= SOUND_COOLDOWN) {
            player.playSound(player.getLocation(), sound, volume, pitch);
            lastSoundPlayed = currentTime;
        }
    }

    /**
     * Khởi tạo giao diện chuyển nhiều loại tài nguyên
     * @param sender Người gửi
     * @param receiver Người nhận
     */
    public MultiTransferGUI(Player sender, Player receiver) {
        this.sender = sender;
        this.receiver = receiver;
        this.config = File.getGUIConfig("transfer"); // Sử dụng config transfer GUI

        // Validate config và input
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config transfer.yml có vấn đề cho MultiTransferGUI");
        }
        if (!validateInput()) {
            Storage.getStorage().getLogger().warning("Input không hợp lệ cho MultiTransferGUI");
        }

        // Đăng ký Listener khi tạo GUI
        registerListener();

        // Bắt đầu task cập nhật GUI mỗi 5 giây
        startRefreshTask();
    }
    
    /**
     * Đăng ký listener cho GUI
     */
    private void registerListener() {
        if (!listenerRegistered) {
            Bukkit.getPluginManager().registerEvents(this, Storage.getStorage());
            listenerRegistered = true;
        }
    }
    
    /**
     * Hủy đăng ký listener
     */
    private void unregisterListener() {
        if (listenerRegistered) {
            HandlerList.unregisterAll(this);
            listenerRegistered = false;
        }
    }

    // Biến tham chiếu đến task cập nhật
    private int refreshTaskId = -1;
    
    /**
     * Bắt đầu task tự động cập nhật GUI
     */
    private void startRefreshTask() {
        refreshTaskId = Bukkit.getScheduler().runTaskTimer(Storage.getStorage(), this::refreshGUI, 100L, 100L).getTaskId(); // 100 ticks = 5 giây
    }
    
    /**
     * Dừng task cập nhật GUI
     */
    private void stopRefreshTask() {
        if (refreshTaskId != -1) {
            Bukkit.getScheduler().cancelTask(refreshTaskId);
            refreshTaskId = -1;
        }
    }
    
    /**
     * Cập nhật giao diện để hiển thị thông tin mới nhất
     */
    private void refreshGUI() {
        // Chỉ cập nhật nếu người chơi vẫn mở inventory của chúng ta
        if (sender.isOnline() && sender.getOpenInventory() != null) {
            try {
                Inventory openTopInventory = sender.getOpenInventory().getTopInventory();
                if (openTopInventory != null && openTopInventory.equals(inventory)) {
            
            // Cập nhật thông tin tài nguyên
            for (Map.Entry<String, Integer> entry : new HashMap<>(transferItems).entrySet()) {
                String material = entry.getKey();
                int amount = entry.getValue();
                
                // Kiểm tra lại số lượng hiện tại người chơi có
                int currentAmount = MineManager.getPlayerBlock(sender, material);
                
                // Nếu số lượng hiện tại ít hơn số lượng đã chọn, cập nhật lại
                if (currentAmount < amount) {
                    if (currentAmount <= 0) {
                        transferItems.remove(material);
                    } else {
                        transferItems.put(material, currentAmount);
                    }
                }
            }

                    // Cập nhật inventory
                    sender.openInventory(getInventory());
                }
            } catch (Exception e) {
                // Bỏ qua lỗi khi lấy top inventory
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Lỗi khi refresh GUI: " + e.getMessage());
                }
            }
        }
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Đọc cấu hình từ YAML
        String title = config.getString("multi_transfer_gui.title", "&8Chuyển nhiều tài nguyên cho &a{receiver}")
                .replace("{receiver}", receiver.getName());
        int size = config.getInt("multi_transfer_gui.size", 54);
        inventory = Bukkit.createInventory(sender, size, GUIText.format(title));

        // Phát âm thanh mở GUI
        try {
            String openSound = config.getString("multi_transfer_gui.open_sound", "BLOCK_CHEST_OPEN:1.0:1.0");
            SoundManager.playSoundFromConfig(sender, openSound);
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }

        // Thêm viền trang trí từ config
        addBorder();

        // Thêm thông tin người chơi từ config
        addPlayerInfo();

        // Thêm các tài nguyên
        addResourceItems();

        // Thêm nút điều khiển từ config
        addControlButtons();

        return inventory;
    }

    /**
     * Validate config để đảm bảo các section cần thiết tồn tại
     * @return true nếu config hợp lệ
     */
    private boolean validateConfig() {
        try {
            // Kiểm tra section chính
            if (config.getConfigurationSection("multi_transfer_gui") == null) {
                Storage.getStorage().getLogger().severe("Không tìm thấy section 'multi_transfer_gui' trong transfer.yml!");
                return false;
            }

            // Kiểm tra các section con cần thiết
            String[] requiredSections = {
                "multi_transfer_gui.player_info",
                "multi_transfer_gui.resource_area",
                "multi_transfer_gui.control_buttons"
            };

            for (String section : requiredSections) {
                if (config.get(section) == null) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy section '" + section + "' trong transfer.yml");
                }
            }

            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi validate config transfer.yml: " + e.getMessage());
            return false;
        }
    }

    /**
     * Validate input parameters
     * @return true nếu input hợp lệ
     */
    private boolean validateInput() {
        try {
            // Kiểm tra người chơi online
            if (!sender.isOnline()) {
                handleError("Sender is not online", false);
                return false;
            }

            if (!receiver.isOnline()) {
                handleError("Receiver is not online", true);
                return false;
            }

            // Kiểm tra người gửi có tài nguyên không
            List<String> materials = MineManager.getPluginBlocks();
            boolean hasAnyResource = false;
            for (String material : materials) {
                if (MineManager.getPlayerBlock(sender, material) > 0) {
                    hasAnyResource = true;
                    break;
                }
            }

            if (!hasAnyResource) {
                // Chỉ ghi log mà không hiển thị tin nhắn lỗi cho người chơi
                handleError("Sender has no resources to transfer", false);
                return false;
            }

            return true;
        } catch (Exception e) {
            handleError("Error validating input: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * Xử lý lỗi và hiển thị thông báo
     * @param error Thông điệp lỗi
     * @param showToPlayer Có hiển thị cho người chơi không
     */
    private void handleError(String error, boolean showToPlayer) {
        Storage.getStorage().getLogger().warning("MultiTransferGUI Error: " + error);
        if (showToPlayer && sender != null && sender.isOnline()) {
            // Chỉ phát âm thanh lỗi mà không hiển thị tin nhắn lỗi cho người chơi
            try {
                String failSound = config.getString("multi_transfer_gui.fail_sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                SoundManager.playSoundFromConfig(sender, failSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
        }
    }

    /**
     * Kiểm tra slot có hợp lệ không
     * @param slot Slot cần kiểm tra
     * @return true nếu hợp lệ
     */
    private boolean isValidSlot(int slot) {
        return slot >= 0 && slot < inventory.getSize();
    }

    /**
     * Thêm viền trang trí cho GUI với error handling
     */
    private void addBorder() {
        String decorateSlots = config.getString("multi_transfer_gui.decorates.slot", "");
        if (!decorateSlots.isEmpty()) {
            String[] slots = decorateSlots.split(", ");

            // Tạo item trang trí
            ItemStack borderItem;
            String materialName = config.getString("multi_transfer_gui.decorates.material", "GRAY_STAINED_GLASS_PANE");

            if (MaterialCompatibility.isPre113()) {
                borderItem = new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), 1, (byte) 7); // GRAY
            } else {
                try {
                    borderItem = new ItemStack(Material.valueOf(materialName));
                } catch (IllegalArgumentException e) {
                    borderItem = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
                }
            }

            ItemMeta borderMeta = borderItem.getItemMeta();
            String displayName = config.getString("multi_transfer_gui.decorates.name", "&r");
            borderMeta.setDisplayName(Chat.colorize(displayName));

            // Áp dụng custom model data nếu có và phiên bản hỗ trợ (1.14+)
            applyCustomModelData(borderMeta, "multi_transfer_gui.decorates.custom-model-data");

            borderItem.setItemMeta(borderMeta);

            // Đặt item vào các slot
            for (String slotStr : slots) {
                try {
                    int slot = Integer.parseInt(slotStr.trim());
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, borderItem);
                    }
                } catch (NumberFormatException e) {
                    // Bỏ qua slot không hợp lệ
                }
            }
        } else {
            // Fallback to default border if config is empty
            try {
                // Sử dụng MaterialCompatibility để tương thích với 1.12.2
                ItemStack borderItem;
                if (MaterialCompatibility.isPre113()) {
                    borderItem = new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), 1, (byte) 15); // BLACK
                } else {
                    borderItem = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
                }

                ItemMeta borderMeta = borderItem.getItemMeta();
                borderMeta.setDisplayName(Chat.colorize("&8"));
                borderItem.setItemMeta(borderMeta);

                // Thêm viền cho hàng đầu tiên và cuối cùng
                for (int i = 0; i < 9; i++) {
                    inventory.setItem(i, borderItem);
                    inventory.setItem(45 + i, borderItem);
                }

                // Thêm viền cho cột đầu tiên và cuối cùng (trừ các góc đã thêm)
                for (int i = 1; i < 5; i++) {
                    inventory.setItem(i * 9, borderItem);
                    inventory.setItem(i * 9 + 8, borderItem);
                }
            } catch (Exception e) {
                // Fallback nếu có lỗi với vật liệu
                Storage.getStorage().getLogger().warning("Lỗi khi tạo viền GUI: " + e.getMessage());
            }
        }
    }
    
    /**
     * Thêm thông tin người chơi từ config
     */
    private void addPlayerInfo() {
        int slot = config.getInt("multi_transfer_gui.player_info.slot", 4);
        String materialConfig = config.getString("multi_transfer_gui.player_info.material", "EMERALD");
        String name = config.getString("multi_transfer_gui.player_info.name", "&a&l{receiver}");
        List<String> lore = config.getStringList("multi_transfer_gui.player_info.lore");

        // Tạo item
        Material material;
        try {
            material = Material.valueOf(materialConfig);
        } catch (IllegalArgumentException e) {
            material = Material.EMERALD;
        }

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(Chat.colorize(name.replace("{receiver}", receiver.getName())));

        // Xử lý lore
        if (lore != null && !lore.isEmpty()) {
            meta.setLore(lore.stream()
                    .map(line -> Chat.colorize(line.replace("{receiver}", receiver.getName())))
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Áp dụng custom model data cho player info
        applyCustomModelData(meta, "multi_transfer_gui.player_info.custom-model-data");

        item.setItemMeta(meta);
        inventory.setItem(slot, item);
    }
    
    /**
     * Thêm các tài nguyên vào GUI từ config
     */
    private void addResourceItems() {
        List<String> materialList = MineManager.getPluginBlocks();

        // Đọc slots từ config
        String slotsConfig = config.getString("multi_transfer_gui.resource_area.slots", "");
        int[] slots;

        if (!slotsConfig.isEmpty()) {
            String[] slotStrings = slotsConfig.split(", ");
            slots = new int[slotStrings.length];
            for (int i = 0; i < slotStrings.length; i++) {
                try {
                    slots[i] = Integer.parseInt(slotStrings[i].trim());
                } catch (NumberFormatException e) {
                    // Fallback to default slots if parsing fails
                    slots = new int[]{10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43};
                    break;
                }
            }
        } else {
            // Default slots
            slots = new int[]{10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43};
        }
        
        int slotIndex = 0;
        
        for (String material : materialList) {
            int currentAmount = MineManager.getPlayerBlock(sender, material);
            
            // Bảo vệ khỏi material là null
            if (material == null) {
                Storage.getStorage().getLogger().warning("Phát hiện material null trong kho của " + sender.getName());
                continue;
            }
            
            // Lấy tên hiển thị của vật liệu, bảo vệ khỏi IndexOutOfBoundsException
            String materialName;
            try {
                materialName = File.getConfig().getString("items." + material, material.contains(";") ? material.split(";")[0] : material);
            } catch (Exception e) {
                Storage.getStorage().getLogger().warning("Lỗi khi xử lý vật liệu " + material + ": " + e.getMessage());
                materialName = material; // Sử dụng tên gốc làm fallback
            }
            
            // Nếu có tài nguyên và còn slot trống, thêm vào giao diện
            if (currentAmount > 0 && slotIndex < slots.length) {
                List<String> lore = new ArrayList<>();
                lore.add("&7Số lượng hiện có: &a" + currentAmount);
                
                // Kiểm tra xem có được chọn chưa
                if (transferItems.containsKey(material)) {
                    int selectedAmount = transferItems.get(material);
                    lore.add("&7Số lượng đã chọn: &e" + selectedAmount);
                    lore.add("&7");
                    lore.add("&aNhấp chuột trái &7để tăng số lượng");
                    lore.add("&cNhấp chuột phải &7để giảm số lượng");
                    lore.add("&eShift + Nhấp chuột phải &7để hủy chọn");
                } else {
                    lore.add("&7");
                    lore.add("&aNhấp để chọn tài nguyên này");
                }
                
                // Tạo ItemStack từ material với xử lý đặc biệt cho khoáng sản
                ItemStack itemStack = createMineralItemStack(material);
                ItemMeta meta = itemStack.getItemMeta();
                meta.setDisplayName(Chat.colorize("&b" + materialName));
                meta.setLore(Chat.colorizewp(lore));
                itemStack.setItemMeta(meta);
                
                int slot = slots[slotIndex];
                InteractiveItem item = new InteractiveItem(itemStack, slot);
                
                // Xử lý khi nhấp chuột
                item.onClick((player, clickType) -> {
                    if (clickType.isLeftClick()) {
                        // Tăng số lượng hoặc thêm mới
                        int amount = transferItems.getOrDefault(material, 0);
                        
                        // Tính toán số lượng cần tăng
                        int increment = 1;
                        if (clickType.isShiftClick()) {
                            increment = 10; // Shift+Click để tăng nhanh
                        }
                        
                        // Kiểm tra số lượng tối đa
                        int maxAmount = MineManager.getPlayerBlock(player, material);
                        amount = Math.min(amount + increment, maxAmount);
                        
                        if (amount > 0) {
                            transferItems.put(material, amount);
                        }
                        
                        // Cập nhật giao diện
                        player.openInventory(getInventory());
                    } else if (clickType.isRightClick()) {
                        if (transferItems.containsKey(material)) {
                            if (clickType.isShiftClick()) {
                                // Shift+Click phải để hủy chọn
                                transferItems.remove(material);
                            } else {
                                // Click phải để giảm số lượng
                                int amount = transferItems.get(material);
                                
                                // Tính toán số lượng cần giảm
                                int decrement = 1;
                                if (amount > 10) {
                                    decrement = 5; // Giảm nhanh nếu số lượng lớn
                                }
                                
                                amount = Math.max(0, amount - decrement);
                                
                                if (amount > 0) {
                                    transferItems.put(material, amount);
                                } else {
                                    transferItems.remove(material);
                                }
                            }
                            
                            // Cập nhật giao diện
                            player.openInventory(getInventory());
                        }
                    }
                });
                
                inventory.setItem(slot, item);
                slotIndex++;
            }
        }
    }
    
    /**
     * Thêm nút điều khiển từ config
     */
    private void addControlButtons() {
        // Nút xác nhận - chỉ hiển thị khi có tài nguyên được chọn
        if (!transferItems.isEmpty()) {
            // Tính tổng số lượng đã chọn
            int totalItems = 0;
            for (int amount : transferItems.values()) {
                totalItems += amount;
            }

            String basePath = "multi_transfer_gui.control_buttons.confirm";
            int slot = config.getInt(basePath + ".slot", 49);
            String materialConfig = config.getString(basePath + ".material", "EMERALD_BLOCK");
            String name = config.getString(basePath + ".name", "&a&lXác nhận chuyển");
            List<String> lore = config.getStringList(basePath + ".lore");

            // Tạo item
            Material material;
            try {
                material = Material.valueOf(materialConfig);
            } catch (IllegalArgumentException e) {
                material = Material.EMERALD_BLOCK;
            }

            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName(Chat.colorize(name.replace("{receiver}", receiver.getName())));

            // Xử lý lore
            if (lore != null && !lore.isEmpty()) {
                meta.setLore(lore.stream()
                        .map(line -> Chat.colorize(line.replace("{receiver}", receiver.getName())))
                        .collect(java.util.stream.Collectors.toList()));
            } else {
                // Lore mặc định nếu không có trong config
                meta.setLore(Arrays.asList(
                    Chat.colorize("&7Chuyển &e" + transferItems.size() + " &7loại tài nguyên"),
                    Chat.colorize("&7Tổng số: &f" + totalItems + " &7tài nguyên"),
                    Chat.colorize("&7Đến: &a" + receiver.getName()),
                    Chat.colorize("&7"),
                    Chat.colorize("&eNhấp để xác nhận")
                ));
            }

            // Áp dụng custom model data cho confirm button
            applyCustomModelData(meta, basePath + ".custom-model-data");

            item.setItemMeta(meta);

            InteractiveItem confirmButton = new InteractiveItem(item, slot).onClick((player, clickType) -> {
                // Kiểm tra nếu người nhận không còn online
                if (!receiver.isOnline()) {
                    player.sendMessage(Chat.colorize("&8[&4&l✕&8] &cNgười chơi &f" + receiver.getName() + " &ckhông còn trực tuyến!"));
                    String failSound = config.getString("multi_transfer_gui.fail_sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    SoundManager.playSoundFromConfig(player, failSound);
                    return;
                }

                // Thực hiện chuyển tài nguyên
                transferAllResources();
            });

            inventory.setItem(confirmButton.getSlot(), confirmButton);
        }

        // Nút hủy
        String basePath = "multi_transfer_gui.control_buttons.cancel";
        int slot = config.getInt(basePath + ".slot", 45);
        String materialConfig = config.getString(basePath + ".material", "BARRIER");
        String name = config.getString(basePath + ".name", "&c&lHủy");
        List<String> lore = config.getStringList(basePath + ".lore");

        // Tạo item
        Material material;
        try {
            material = Material.valueOf(materialConfig);
        } catch (IllegalArgumentException e) {
            material = Material.BARRIER;
        }

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(Chat.colorize(name.replace("{receiver}", receiver.getName())));

        // Xử lý lore
        if (lore != null && !lore.isEmpty()) {
            meta.setLore(lore.stream()
                    .map(line -> Chat.colorize(line.replace("{receiver}", receiver.getName())))
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Áp dụng custom model data cho cancel button
        applyCustomModelData(meta, basePath + ".custom-model-data");

        item.setItemMeta(meta);

        InteractiveItem cancelButton = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            player.closeInventory();
            // Sử dụng Bukkit scheduler với lambda thay vì BukkitRunnable
            Bukkit.getScheduler().runTask(Storage.getStorage(), () ->
                player.openInventory(new PersonalStorage(player).getInventory())
            );
        });

        inventory.setItem(cancelButton.getSlot(), cancelButton);
    }

    /**
     * Áp dụng custom model data cho ItemMeta nếu phiên bản hỗ trợ
     * @param meta ItemMeta cần áp dụng
     * @param configPath Đường dẫn config cho custom model data
     */
    private void applyCustomModelData(ItemMeta meta, String configPath) {
        if (meta == null) return;

        try {
            // Kiểm tra phiên bản có hỗ trợ Custom Model Data không (1.14+)
            if (MaterialCompatibility.isPre113() || nmsAssistant.isVersionLessThan(14)) {
                return; // Không hỗ trợ trong phiên bản cũ
            }

            // Đọc custom model data từ config
            if (config.contains(configPath)) {
                int customModelData = config.getInt(configPath, -1);
                if (customModelData > 0) {
                    // Sử dụng AdvancedCompatibility để set custom model data an toàn
                    AdvancedCompatibility.setCustomModelData(meta, customModelData);

                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đã áp dụng CustomModelData " + customModelData + " cho " + configPath);
                    }
                }
            }
        } catch (Exception e) {
            handleError("Failed to apply custom model data: " + e.getMessage(), false);
        }
    }

    /**
     * Kiểm tra người chơi có tồn tại trong database không
     * @param playerName Tên người chơi cần kiểm tra
     * @return true nếu người chơi tồn tại, false nếu không
     */
    private boolean checkPlayerExists(String playerName) {
        if (playerName == null || playerName.isEmpty()) {
            return false;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = Storage.db.getConnection();
            if (conn == null) {
                Storage.getStorage().getLogger().warning("Không thể kết nối đến cơ sở dữ liệu để kiểm tra người chơi.");
                return false;
            }
            
            // Tăng timeout cho SQLite để tránh SQLITE_BUSY
            try {
                conn.createStatement().execute("PRAGMA busy_timeout = 30000");
            } catch (Exception e) {
                // Bỏ qua nếu không hỗ trợ
            }
            
            // Kiểm tra người chơi tồn tại
            ps = conn.prepareStatement("SELECT COUNT(*) FROM " + Storage.db.table + " WHERE player = ?");
            ps.setQueryTimeout(10); // Timeout 10 giây cho query
            ps.setString(1, playerName);
            rs = ps.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
            
            return false;
        } catch (SQLException e) {
            Storage.getStorage().getLogger().warning("Lỗi khi kiểm tra người chơi trong database: " + e.getMessage());
            return false;
        } finally {
            try {
                if (rs != null) rs.close();
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Lỗi khi đóng kết nối database: " + e.getMessage());
            }
        }
    }
    
    /**
     * Xử lý chuyển tất cả tài nguyên cho người chơi
     */
    private void transferAllResources() {
        // Kiểm tra xem receiver có tồn tại và online không
        Objects.requireNonNull(sender, "Người gửi không thể null");
        if (receiver == null || !receiver.isOnline()) {
            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.player_not_online"))
                    .replace("#player#", receiver != null ? receiver.getName() : "người chơi")));
            try {
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                if (failSoundConfig != null && !failSoundConfig.isEmpty()) {
                    String[] parts = failSoundConfig.split(":");
                    org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                    float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 1.0f;
                    float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;
                    sender.playSound(sender.getLocation(), sound, volume, pitch);
                }
            } catch (Exception e) {
                // Bỏ qua nếu âm thanh không hỗ trợ
            }
            return;
        }
        
        // Kiểm tra xem người nhận có tồn tại trong database không
        if (!checkPlayerExists(receiver.getName())) {
            sender.sendMessage(Chat.colorize("&8[&c&l✕&8] &cKhông thể chuyển tài nguyên: Người chơi &f" + receiver.getName() + " &cchưa từng tham gia máy chủ!"));
            try {
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                if (failSoundConfig != null && !failSoundConfig.isEmpty()) {
                    com.hongminh54.storage.Utils.SoundCompatibility.playSoundFromConfig(sender, failSoundConfig);
                }
            } catch (Exception e) {
                // Bỏ qua nếu âm thanh không hỗ trợ
            }
            return;
        }
        
        // Kiểm tra xem người chơi có quyền chuyển tài nguyên không
        if (!com.hongminh54.storage.Utils.TransferManager.canTransfer(sender)) {
            sender.sendMessage(Chat.colorize("&8[&c&l⏱&8] &cBạn cần đợi một chút trước khi thực hiện giao dịch tiếp theo."));
            
            // Phát âm thanh thất bại
            try {
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                String[] soundParts = failSoundConfig.split(":");
                org.bukkit.Sound sound = org.bukkit.Sound.valueOf(soundParts[0]);
                float volume = soundParts.length > 1 ? Float.parseFloat(soundParts[1]) : 1.0f;
                float pitch = soundParts.length > 2 ? Float.parseFloat(soundParts[2]) : 1.0f;
                
                sender.playSound(sender.getLocation(), sound, volume, pitch);
            } catch (Exception e) {
                // Bỏ qua nếu không hỗ trợ
            }
            return;
        }
        
        // Kiểm tra xem người chơi đã có giao dịch đang xử lý không
        if (com.hongminh54.storage.Utils.TransferDelayManager.isPlayerTransferring(sender)) {
            sender.sendMessage(Chat.colorize("&8[&c&l⏱&8] &cBạn đang có giao dịch đang xử lý. Vui lòng đợi hoàn tất."));
            return;
        }
        
        // Tạo Map chứa tất cả tài nguyên cần chuyển
        final Map<String, Integer> resourcesToTransfer = new HashMap<>();
        
        // Xác nhận các tài nguyên được chọn
        for (Map.Entry<String, Integer> entry : transferItems.entrySet()) {
            String material = entry.getKey();
            int requestedAmount = entry.getValue();
            
            if (requestedAmount <= 0) {
                continue;
            }
            
            // Kiểm tra số lượng người gửi có
            int currentAmount = MineManager.getPlayerBlock(sender, material);
            if (currentAmount < requestedAmount) {
                requestedAmount = currentAmount; // Chỉ chuyển số lượng hiện có
            }
            
            if (requestedAmount > 0) {
                resourcesToTransfer.put(material, requestedAmount);
            }
        }
        
        if (resourcesToTransfer.isEmpty()) {
            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.no_resource_selected",
                    "&8[&c&l✕&8] &cBạn chưa chọn tài nguyên nào để chuyển"))));
            try {
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                if (failSoundConfig != null && !failSoundConfig.isEmpty()) {
                    String[] parts = failSoundConfig.split(":");
                    org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                    float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 1.0f;
                    float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;
                    sender.playSound(sender.getLocation(), sound, volume, pitch);
                }
            } catch (Exception e) {
                // Bỏ qua nếu âm thanh không hỗ trợ
            }
            return;
        }
        
        // Đọc phần trăm chuyển tài nguyên từ cấu hình
        int transferPercentage = File.getConfig().getInt("settings.transfer_percentage", 25);
        
        // Thông báo về phần trăm chuyển tài nguyên
        String transferLimitMessage = Objects.requireNonNull(File.getMessage().getString("user.action.transfer.transfer_limit", 
                "&8[&e&l!&8] &eGiới hạn chuyển: &a#percentage#% &etài nguyên mỗi lần để tránh lỡ tay"))
                .replace("#percentage#", String.valueOf(transferPercentage));
        sender.sendMessage(Chat.colorize(transferLimitMessage));
        
        // Đóng giao diện người dùng khi bắt đầu quá trình chuyển
        sender.closeInventory();
        
        // Sử dụng TransferDelayManager để thêm delay - đặc biệt đối với chuyển nhiều tài nguyên
        // Do phương thức registerTransfer chỉ hỗ trợ một material, nên chúng ta tạo một TransferCallback tùy chỉnh
        com.hongminh54.storage.Utils.TransferDelayManager.registerTransfer(
            sender, receiver, "multi_transfer", 0, 
            new com.hongminh54.storage.Utils.TransferDelayManager.TransferCallback() {
                @Override
                public void onTransferReady(Player sender, Player receiver, String material, int amount) {
                    // Đã hết thời gian delay, bây giờ thực hiện chuyển tài nguyên
                    // Sử dụng khóa đồng bộ hóa để ngăn việc chuyển tài nguyên đồng thời
                    String transferLockKey = "multi_transfer_lock_" + sender.getUniqueId() + "_" + receiver.getUniqueId();
                    synchronized (transferLockKey.intern()) {
                        // Sử dụng phương thức TransferManager.transferResourcesWithConsistencyCheck để chuyển tất cả tài nguyên một lần
                        // Điều này giúp đảm bảo tính nhất quán và an toàn của dữ liệu
                        Map<String, Integer> transferredResources = TransferManager.transferResourcesWithConsistencyCheck(sender, receiver, resourcesToTransfer);
                                    
                        // Kiểm tra kết quả chuyển
                        boolean anySuccess = !transferredResources.isEmpty();
                        int totalTransferred = 0;
                        
                        if (anySuccess) {
                            // Thông báo thành công cho người gửi
                            StringBuilder detailMsg = new StringBuilder();
                            
                            // Xử lý thông tin các tài nguyên đã chuyển
                            for (Map.Entry<String, Integer> entry : transferredResources.entrySet()) {
                                String materialKey = entry.getKey();
                                int transferredAmount = entry.getValue();
                                totalTransferred += transferredAmount;
                                
                                // Lấy tên hiển thị của vật liệu
                                String materialName = File.getConfig().getString("items." + materialKey, materialKey.split(";")[0]);
                                detailMsg.append("\n&7- &f").append(transferredAmount).append(" &a").append(materialName);
                            }
                            
                            String successMsg = File.getMessage().getString("user.action.transfer.multi_success", 
                                    "&8[&a&l✓&8] &aĐã chuyển các tài nguyên sau cho &e#player#:#details#");
                                    
                            String formattedSuccessMsg = successMsg
                                    .replace("#player#", receiver.getName())
                                    .replace("#details#", detailMsg.toString())
                                    .replace("#total_amount#", String.valueOf(totalTransferred))
                                    .replace("#count#", String.valueOf(transferredResources.size()));
                                    
                            sender.sendMessage(Chat.colorize(formattedSuccessMsg));
                            
                            // Thông báo cho người nhận
                            String receivedMsg = File.getMessage().getString("user.action.transfer.multi_receive", 
                                    "&8[&a&l✓&8] &aBạn nhận được các tài nguyên sau từ &e#player#:#details#");
                                    
                            String formattedReceivedMsg = receivedMsg
                                    .replace("#player#", sender.getName())
                                    .replace("#details#", detailMsg.toString())
                                    .replace("#total_amount#", String.valueOf(totalTransferred))
                                    .replace("#count#", String.valueOf(transferredResources.size()));
                                    
                            receiver.sendMessage(Chat.colorize(formattedReceivedMsg));
                            
                            // Phát hiệu ứng thành công
                            playMultiTransferEffects(sender, receiver);
                        } else {
                            // Thông báo thất bại nếu không chuyển được tài nguyên nào
                            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.failed", 
                                    "&8[&c&l✕&8] &cKhông thể chuyển tài nguyên: &f#reason#"))
                                    .replace("#reason#", "Kho của người nhận đã đầy hoặc bạn không có tài nguyên nào để chuyển")));
                            
                            // Phát âm thanh thất bại
                            try {
                                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                                if (failSoundConfig != null && !failSoundConfig.isEmpty()) {
                                    String[] parts = failSoundConfig.split(":");
                                    org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                                    float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 1.0f;
                                    float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;
                                    sender.playSound(sender.getLocation(), sound, volume, pitch);
                                }
                            } catch (Exception e) {
                                // Bỏ qua nếu âm thanh không hỗ trợ
                            }
                        }
                    }
                }
            }
        );
    }
    
    /**
     * Phát hiệu ứng khi chuyển nhiều tài nguyên
     */
    private void playMultiTransferEffects(Player sender, Player receiver) {
        if (sender == null || receiver == null || !sender.isOnline() || !receiver.isOnline()) {
            return;
        }
        
        try {
            // Đọc cấu hình hiệu ứng
            int maxParticleCount = File.getConfig().getInt("settings.max_particle_count", 50);
            
            // Phát âm thanh cho người gửi
            String senderSoundConfig = File.getConfig().getString("effects.transfer_success.sender_sound", "ENTITY_PLAYER_LEVELUP:0.5:1.2");
            if (senderSoundConfig != null && !senderSoundConfig.isEmpty()) {
                try {
                    String[] parts = senderSoundConfig.split(":");
                    org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                    float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 0.5f;
                    float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.2f;
                    sender.playSound(sender.getLocation(), sound, volume, pitch);
                } catch (Exception e) {
                    // Bỏ qua nếu âm thanh không hỗ trợ
                }
            }
            
            // Phát âm thanh cho người nhận
            String receiverSoundConfig = File.getConfig().getString("effects.transfer_success.receiver_sound", "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0");
            if (receiverSoundConfig != null && !receiverSoundConfig.isEmpty()) {
                try {
                    String[] parts = receiverSoundConfig.split(":");
                    org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                    float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 0.5f;
                    float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;
                    receiver.playSound(receiver.getLocation(), sound, volume, pitch);
                } catch (Exception e) {
                    // Bỏ qua nếu âm thanh không hỗ trợ
                }
            }
            
            // Hiệu ứng hạt cho người gửi
            applyParticleEffect(sender, "effects.transfer_success.sender_particle", maxParticleCount);
            
            // Hiệu ứng hạt cho người nhận
            applyParticleEffect(receiver, "effects.transfer_success.receiver_particle", maxParticleCount);
            
            // Hiệu ứng bổ sung cho giao dịch lớn (sử dụng giá trị cố định)
            int particleBoostThreshold = File.getConfig().getInt("settings.large_transfer_threshold", 100);
            boolean isLargeTransfer = false;
            
            // Tính tổng số tài nguyên chuyển
            int total = 0;
            for (Integer value : transferItems.values()) {
                total += value;
            }
            
            if (total > particleBoostThreshold) {
                isLargeTransfer = true;
                String largeSenderParticleConfig = File.getConfig().getString("effects.large_transfer.sender_particle", "SPELL_WITCH:0.2:0.2:0.2:0.05");
                String largeReceiverParticleConfig = File.getConfig().getString("effects.large_transfer.receiver_particle", "TOTEM:0.5:0.5:0.5:0.1");
                
                if (largeSenderParticleConfig != null && !largeSenderParticleConfig.isEmpty()) {
                    try {
                        String[] parts = largeSenderParticleConfig.split(":");
                        org.bukkit.Particle particle = org.bukkit.Particle.valueOf(parts[0]);
                        double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.2;
                        double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.2;
                        double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.2;
                        double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.05;
                        
                        sender.getWorld().spawnParticle(particle, sender.getLocation().add(0, 1.5, 0),
                                Math.min(20, maxParticleCount), offsetX, offsetY, offsetZ, speed);
                    } catch (Exception e) {
                        // Bỏ qua nếu hiệu ứng không hỗ trợ
                    }
                }
                
                if (largeReceiverParticleConfig != null && !largeReceiverParticleConfig.isEmpty()) {
                    try {
                        String[] parts = largeReceiverParticleConfig.split(":");
                        org.bukkit.Particle particle = org.bukkit.Particle.valueOf(parts[0]);
                        double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.5;
                        double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.5;
                        double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.5;
                        double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.1;
                        
                        receiver.getWorld().spawnParticle(particle, receiver.getLocation().add(0, 1.5, 0),
                                Math.min(20, maxParticleCount), offsetX, offsetY, offsetZ, speed);
                    } catch (Exception e) {
                        // Bỏ qua nếu hiệu ứng không hỗ trợ
                    }
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi tổng thể
            Storage.getStorage().getLogger().warning("Lỗi khi phát hiệu ứng chuyển tài nguyên: " + e.getMessage());
        }
    }
    
    /**
     * Áp dụng hiệu ứng hạt cho người chơi
     * @param player Người chơi nhận hiệu ứng
     * @param configPath Đường dẫn đến cấu hình hiệu ứng trong file config
     * @param maxParticleCount Số lượng hạt tối đa
     */
    private void applyParticleEffect(Player player, String configPath, int maxParticleCount) {
        if (player == null || !player.isOnline() || maxParticleCount <= 0) {
            return;
        }
        
        try {
            String particleConfig = File.getConfig().getString(configPath, "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:20");
            if (particleConfig != null && !particleConfig.isEmpty()) {
                String[] parts = particleConfig.split(":");
                if (parts.length < 1) return;
                
                org.bukkit.Particle particle = org.bukkit.Particle.valueOf(parts[0]);
                double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.5;
                double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.5;
                double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.5;
                double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.1;
                int count = parts.length > 5 ? Math.min(Integer.parseInt(parts[5]), maxParticleCount) : Math.min(20, maxParticleCount);
                
                player.getWorld().spawnParticle(particle, player.getLocation().add(0, 1, 0), 
                        count, offsetX, offsetY, offsetZ, speed);
            }
        } catch (Exception e) {
            // Bỏ qua nếu hiệu ứng hạt không hỗ trợ
        }
    }
    
    /**
     * Lấy người chơi gửi
     * @return Người gửi
     */
    public Player getSender() {
        return sender;
    }
    
    /**
     * Lấy người chơi nhận
     * @return Người nhận
     */
    public Player getReceiver() {
        return receiver;
    }
    
    /**
     * Lấy danh sách tài nguyên sẽ chuyển
     * @return Danh sách tài nguyên
     */
    public Map<String, Integer> getTransferItems() {
        return transferItems;
    }

    /**
     * Xử lý sự kiện click vào inventory
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        // Kiểm tra xem view có chứa inventory của GUI này không
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory == null || !topInventory.equals(inventory)) {
            return;
        }
        
        // Hủy tất cả các sự kiện click, bất kể vị trí nào trong GUI
        event.setCancelled(true);
        
        // Cập nhật inventory ngay lập tức để đảm bảo thay đổi được áp dụng
        if (event.getWhoClicked() instanceof Player) {
            ((Player) event.getWhoClicked()).updateInventory();
        }
        
        // Nếu không phải click vào inventory của GUI này hoặc không có item, bỏ qua xử lý
        if (event.getCurrentItem() == null || !event.getClickedInventory().equals(inventory)) {
            return;
        }
        
        // Xử lý các tương tác với item trong GUI
        Player player = (Player) event.getWhoClicked();
        ClickType clickType = event.getClick();
        int slot = event.getRawSlot();
        
        // Nếu plugin có chế độ debug, log thông tin click
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Player " + player.getName() + " clicked on slot " + slot + 
                " in MultiTransferGUI with type " + clickType.name());
        }
        
        // InteractiveItems sẽ tự xử lý các item khác qua registerEvents
    }
    
    /**
     * Xử lý sự kiện đóng inventory
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().equals(inventory)) {
            // Hủy đăng ký listener khi đóng giao diện - Sử dụng Bukkit scheduler với lambda thay vì BukkitRunnable
            Bukkit.getScheduler().runTaskLater(Storage.getStorage(), () -> {
                unregisterListener();
                stopRefreshTask(); // Dừng task cập nhật khi đóng GUI
            }, 1L);
        }
    }
    
    /**
     * Xử lý sự kiện kéo vật phẩm trong inventory
     * Ngăn người chơi kéo vật phẩm trong GUI
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryDrag(InventoryDragEvent event) {
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory != null && topInventory.equals(inventory)) {
            // Hủy tất cả các sự kiện kéo vật phẩm trong GUI này
            event.setCancelled(true);
            
            // Cập nhật inventory ngay lập tức để đảm bảo thay đổi được áp dụng
            if (event.getWhoClicked() instanceof Player) {
                ((Player) event.getWhoClicked()).updateInventory();
            }
        }
    }

    /**
     * Tạo ItemStack từ material string với xử lý tương thích đa phiên bản
     * @param materialString Material string (có thể có format "MATERIAL;DATA")
     * @return ItemStack tương ứng
     */
    private ItemStack createItemStackFromMaterial(String materialString) {
        // Sử dụng function mới từ MaterialCompatibility
        return MaterialCompatibility.createCompatibleItemStack(materialString);
    }

    /**
     * Tạo ItemStack cho khoáng sản với xử lý đặc biệt
     * @param materialKey Key của material trong config (format: "MATERIAL;DATA")
     * @return ItemStack tương ứng với khoáng sản
     */
    private ItemStack createMineralItemStack(String materialKey) {
        try {
            // Kiểm tra xem có cấu hình drop cho material này không
            String dropMaterial = File.getConfig().getString("blocks." + materialKey + ".drop");
            if (dropMaterial != null && !dropMaterial.isEmpty()) {
                // Sử dụng drop material để hiển thị (ví dụ: COAL_ORE -> COAL)
                ItemStack dropItem = createItemStackFromMaterial(dropMaterial);
                if (dropItem != null && dropItem.getType() != Material.STONE) {
                    return dropItem;
                }
            }

            // Nếu không có drop config, sử dụng material gốc
            ItemStack originalItem = createItemStackFromMaterial(materialKey);
            if (originalItem != null && originalItem.getType() != Material.STONE) {
                return originalItem;
            }

            // Fallback: thử tìm material tương ứng dựa trên tên
            String materialName = materialKey.split(";")[0];

            // Xử lý một số trường hợp đặc biệt
            if (materialName.contains("_ORE")) {
                // Thử tìm material không có _ORE (ví dụ: IRON_ORE -> IRON_INGOT)
                String baseName = materialName.replace("_ORE", "");
                String[] possibleMaterials = {
                    baseName + "_INGOT",
                    baseName,
                    baseName + "_BLOCK"
                };

                for (String possibleMaterial : possibleMaterials) {
                    try {
                        Material mat = Material.valueOf(possibleMaterial);
                        return new ItemStack(mat, 1);
                    } catch (IllegalArgumentException ignored) {
                        // Thử material tiếp theo
                    }
                }
            }

            // Fallback cuối cùng
            return createItemStackFromMaterial(materialKey);

        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Lỗi khi tạo ItemStack cho khoáng sản " + materialKey + ": " + e.getMessage());
            }
            return new ItemStack(Material.STONE, 1);
        }
    }
}