package com.hongminh54.storage.GUI;

import java.util.AbstractMap.SimpleEntry;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.AdvancedCompatibility;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.InventoryCompatibility;
import com.hongminh54.storage.Utils.MaterialCompatibility;
import com.hongminh54.storage.Utils.PlayerCompatibility;
import com.hongminh54.storage.Utils.SoundManager;
import com.cryptomorin.xseries.XMaterial;

/**
 * Giao diện chuyển tài nguyên giữa người chơi
 */
public class TransferGUI implements IGUI, Listener {

    private final Player sender;
    private final Player receiver;
    private final String material;
    private final FileConfiguration config;
    private Inventory inventory;
    private boolean listenerRegistered = false;

    // NMS Assistant cho version checking
    private static final NMSAssistant nmsAssistant = new NMSAssistant();

    /**
     * Khởi tạo giao diện chuyển tài nguyên
     * @param sender Người gửi tài nguyên
     * @param receiver Người nhận tài nguyên
     * @param material Loại tài nguyên
     */
    public TransferGUI(Player sender, Player receiver, String material) {
        this.sender = Objects.requireNonNull(sender, "Người gửi không thể là null");
        this.receiver = Objects.requireNonNull(receiver, "Người nhận không thể là null");
        this.material = Objects.requireNonNull(material, "Vật liệu không thể là null");
        this.config = File.getGUIConfig("transfer"); // Sử dụng config transfer GUI

        // Validate config và input
        if (!validateConfig()) {
            Storage.getStorage().getLogger().warning("Config transfer.yml có vấn đề cho TransferGUI");
        }
        if (!validateInput()) {
            Storage.getStorage().getLogger().warning("Input không hợp lệ cho TransferGUI");
        }

        // Đăng ký Listener khi tạo GUI
        registerListener();
    }
    
    /**
     * Đăng ký listener cho GUI
     */
    private void registerListener() {
        if (!listenerRegistered) {
            Bukkit.getPluginManager().registerEvents(this, Storage.getStorage());
            listenerRegistered = true;
        }
    }
    
    /**
     * Hủy đăng ký listener
     */
    private void unregisterListener() {
        if (listenerRegistered) {
            HandlerList.unregisterAll(this);
            listenerRegistered = false;
        }
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Đọc cấu hình từ YAML
        String title = config.getString("transfer_gui.title", "&8Chuyển tài nguyên cho &a{receiver}")
                .replace("{receiver}", receiver.getName());
        int size = config.getInt("transfer_gui.size", 45);
        inventory = Bukkit.createInventory(sender, size, GUIText.format(title));

        // Phát âm thanh mở GUI
        try {
            String openSound = config.getString("transfer_gui.open_sound", "BLOCK_CHEST_OPEN:1.0:1.0");
            SoundManager.playSoundFromConfig(sender, openSound);
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }

        // Thêm viền trang trí từ config
        setupDecorItems();
        
        // Lấy thông tin số lượng tài nguyên hiện có
        int currentAmount = MineManager.getPlayerBlock(sender, material);
        String materialName = Objects.requireNonNull(File.getConfig().getString("items." + material, material.split(";")[0]));
        
        // Kiểm tra nếu không có tài nguyên
        if (currentAmount <= 0) {
            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                    .replace("#material#", materialName)
                    .replace("#amount#", "0")));
            
            // Phát âm thanh thất bại
            try {
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                com.hongminh54.storage.Utils.SoundCompatibility.playSoundFromConfig(sender, failSoundConfig);
            } catch (Exception e) {
                // Phát âm thanh cảnh báo
                com.hongminh54.storage.Utils.SoundCompatibility.playSound(sender, "NOTE_BASS", 0.5f, 0.8f);
            }
            
            // Đóng giao diện và mở lại giao diện kho cá nhân sau 1 tick
            new BukkitRunnable() {
                @Override
                public void run() {
                    sender.closeInventory();
                    // Có thể mở lại giao diện kho cá nhân nếu cần
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            sender.openInventory(new PersonalStorage(sender).getInventory());
                        }
                    }.runTask(Storage.getStorage());
                }
            }.runTask(Storage.getStorage());
            
            // Trả về giao diện trống, người chơi sẽ không thấy nó vì closeInventory đã được gọi
            return inventory;
        }

        // Thêm thông tin người chơi từ config
        addPlayerInfo(materialName, currentAmount);

        // Thêm các nút số lượng từ config
        addQuantityButtons(materialName, currentAmount);

        // Thêm nút điều khiển từ config
        addControlButtons(materialName);
        
        return inventory;
    }

    /**
     * Validate config để đảm bảo các section cần thiết tồn tại
     * @return true nếu config hợp lệ
     */
    private boolean validateConfig() {
        try {
            // Kiểm tra section chính
            if (config.getConfigurationSection("transfer_gui") == null) {
                Storage.getStorage().getLogger().severe("Không tìm thấy section 'transfer_gui' trong transfer.yml!");
                return false;
            }

            // Kiểm tra các section con cần thiết
            String[] requiredSections = {
                "transfer_gui.quantity_buttons",
                "transfer_gui.player_info",
                "transfer_gui.control_buttons"
            };

            for (String section : requiredSections) {
                if (config.get(section) == null) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy section '" + section + "' trong transfer.yml");
                }
            }

            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi validate config transfer.yml: " + e.getMessage());
            return false;
        }
    }

    /**
     * Validate input parameters
     * @return true nếu input hợp lệ
     */
    private boolean validateInput() {
        try {
            // Kiểm tra người chơi online
            if (!sender.isOnline()) {
                handleError("Sender is not online", false);
                return false;
            }

            if (!receiver.isOnline()) {
                handleError("Receiver is not online", true);
                return false;
            }

            // Kiểm tra material hợp lệ
            if (material == null || material.trim().isEmpty()) {
                handleError("Material is null or empty", true);
                return false;
            }

            // Kiểm tra người gửi có tài nguyên không
            int currentAmount = MineManager.getPlayerBlock(sender, material);
            if (currentAmount <= 0) {
                handleError("Sender has no resources to transfer", true);
                return false;
            }

            return true;
        } catch (Exception e) {
            handleError("Error validating input: " + e.getMessage(), false);
            return false;
        }
    }

    /**
     * Xử lý lỗi và hiển thị thông báo
     * @param error Thông điệp lỗi
     * @param showToPlayer Có hiển thị cho người chơi không
     */
    private void handleError(String error, boolean showToPlayer) {
        Storage.getStorage().getLogger().warning("TransferGUI Error: " + error);
        if (showToPlayer && sender != null && sender.isOnline()) {
            // Chỉ phát âm thanh lỗi mà không hiển thị tin nhắn lỗi cho người chơi
            try {
                String failSound = config.getString("transfer_gui.fail_sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                SoundManager.playSoundFromConfig(sender, failSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
        }
    }

    /**
     * Kiểm tra slot có hợp lệ không
     * @param slot Slot cần kiểm tra
     * @return true nếu hợp lệ
     */
    private boolean isValidSlot(int slot) {
        return slot >= 0 && slot < inventory.getSize();
    }

    /**
     * Thiết lập các vật phẩm trang trí từ config với error handling
     */
    private void setupDecorItems() {
        try {
            String decorateSlots = config.getString("transfer_gui.decorates.slot", "");
            if (!decorateSlots.isEmpty()) {
                String[] slots = decorateSlots.split(", ");

                // Tạo item trang trí với compatibility
                ItemStack borderItem = createCompatibleDecorativeItem();

                // Đặt item vào các slot với validation và InteractiveItem
                for (String slotStr : slots) {
                    try {
                        int slot = Integer.parseInt(slotStr.trim());
                        if (isValidSlot(slot)) {
                            // Tạo InteractiveItem để ngăn người chơi lấy item ra
                            InteractiveItem decorativeItem = new InteractiveItem(borderItem.clone(), slot);
                            inventory.setItem(decorativeItem.getSlot(), decorativeItem);
                        } else {
                            handleError("Invalid decorative slot: " + slot, false);
                        }
                    } catch (NumberFormatException e) {
                        handleError("Invalid slot format: " + slotStr, false);
                    }
                }
            }
        } catch (Exception e) {
            handleError("Failed to setup decorative items: " + e.getMessage(), false);
        }
    }

    /**
     * Tạo item trang trí tương thích với các phiên bản Minecraft
     * @return ItemStack trang trí
     */
    private ItemStack createCompatibleDecorativeItem() {
        try {
            String materialName = config.getString("transfer_gui.decorates.material", "GRAY_STAINED_GLASS_PANE");
            String displayName = config.getString("transfer_gui.decorates.name", "&r");

            // Sử dụng function mới từ MaterialCompatibility
            ItemStack borderItem = MaterialCompatibility.createCompatibleItemStack(materialName);

            ItemMeta borderMeta = borderItem.getItemMeta();
            if (borderMeta != null) {
                borderMeta.setDisplayName(Chat.colorize(displayName));

                // Áp dụng custom model data nếu có và phiên bản hỗ trợ (1.14+)
                applyCustomModelData(borderMeta, "transfer_gui.decorates.custom-model-data");

                borderItem.setItemMeta(borderMeta);
            }

            return borderItem;
        } catch (Exception e) {
            handleError("Failed to create decorative item: " + e.getMessage(), false);
            // Fallback item
            return new ItemStack(Material.STONE);
        }
    }
    
    /**
     * Thêm thông tin người chơi từ config
     * @param materialName Tên vật liệu
     * @param currentAmount Số lượng hiện có
     */
    private void addPlayerInfo(String materialName, int currentAmount) {
        int slot = config.getInt("transfer_gui.player_info.slot", 4);
        String materialConfig = config.getString("transfer_gui.player_info.material", "EMERALD");
        String name = config.getString("transfer_gui.player_info.name", "&a&l{receiver}");
        List<String> lore = config.getStringList("transfer_gui.player_info.lore");

        // Tạo item với xử lý tương thích đa phiên bản
        ItemStack item = createItemStackFromMaterial(materialConfig);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(Chat.colorize(name
                .replace("{receiver}", receiver.getName())
                .replace("{material}", materialName)
                .replace("{current_amount}", String.valueOf(currentAmount))));

        // Xử lý lore
        if (lore != null && !lore.isEmpty()) {
            meta.setLore(lore.stream()
                    .map(line -> Chat.colorize(line
                            .replace("{receiver}", receiver.getName())
                            .replace("{material}", materialName)
                            .replace("{current_amount}", String.valueOf(currentAmount))))
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Áp dụng custom model data cho player info
        applyCustomModelData(meta, "transfer_gui.player_info.custom-model-data");

        item.setItemMeta(meta);

        // Tạo InteractiveItem để ngăn người chơi lấy item ra (không có click handler)
        InteractiveItem interactiveItem = new InteractiveItem(item, slot);
        inventory.setItem(interactiveItem.getSlot(), interactiveItem);
    }

    /**
     * Thêm các nút số lượng từ config
     * @param materialName Tên vật liệu
     * @param currentAmount Số lượng hiện có
     */
    private void addQuantityButtons(String materialName, int currentAmount) {
        // Thêm các nút số lượng cố định
        addQuantityButton("quantity_1", 1, materialName);
        addQuantityButton("quantity_5", 5, materialName);
        addQuantityButton("quantity_10", 10, materialName);
        addQuantityButton("quantity_32", 32, materialName);
        addQuantityButton("quantity_64", 64, materialName);

        // Thêm nút tùy chỉnh
        addCustomQuantityButton(materialName);

        // Thêm nút chuyển tất cả
        addAllQuantityButton(materialName, currentAmount);
    }

    /**
     * Thêm nút số lượng từ config
     * @param configKey Key trong config
     * @param amount Số lượng
     * @param materialName Tên vật liệu
     */
    private void addQuantityButton(String configKey, int amount, String materialName) {
        String basePath = "transfer_gui.quantity_buttons." + configKey;
        int slot = config.getInt(basePath + ".slot", -1);
        if (slot == -1) return;

        String materialConfig = config.getString(basePath + ".material", "STONE");
        String name = config.getString(basePath + ".name", "&a" + amount + " {material}");
        List<String> lore = config.getStringList(basePath + ".lore");

        // Tạo item với xử lý tương thích đa phiên bản
        ItemStack item = createItemStackFromMaterial(materialConfig);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(Chat.colorize(name
                .replace("{material}", materialName)
                .replace("{receiver}", receiver.getName())
                .replace("{amount}", String.valueOf(amount))));

        // Xử lý lore
        if (lore != null && !lore.isEmpty()) {
            meta.setLore(lore.stream()
                    .map(line -> Chat.colorize(line
                            .replace("{material}", materialName)
                            .replace("{receiver}", receiver.getName())
                            .replace("{amount}", String.valueOf(amount))))
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Áp dụng custom model data cho quantity buttons
        applyCustomModelData(meta, basePath + ".custom-model-data");

        item.setItemMeta(meta);

        // Tạo InteractiveItem với click handler
        InteractiveItem interactiveItem = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            int currentAmount = MineManager.getPlayerBlock(player, material);
            if (currentAmount < amount) {
                player.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                        .replace("#material#", materialName)
                        .replace("#amount#", String.valueOf(currentAmount))));

                // Phát âm thanh thất bại
                try {
                    String failSound = config.getString("transfer_gui.fail_sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    SoundManager.playSoundFromConfig(player, failSound);
                } catch (Exception e) {
                    // Bỏ qua lỗi âm thanh
                }
                return;
            }

            // Xử lý chuyển tài nguyên
            transferResource(player, receiver, material, amount);
        });

        inventory.setItem(interactiveItem.getSlot(), interactiveItem);
    }

    /**
     * Thêm nút tùy chỉnh từ config
     * @param materialName Tên vật liệu
     */
    private void addCustomQuantityButton(String materialName) {
        String basePath = "transfer_gui.quantity_buttons.custom";
        int slot = config.getInt(basePath + ".slot", -1);
        if (slot == -1) return;

        String materialConfig = config.getString(basePath + ".material", "ANVIL");
        String name = config.getString(basePath + ".name", "&aTùy chỉnh số lượng");
        List<String> lore = config.getStringList(basePath + ".lore");

        // Tạo item với xử lý tương thích đa phiên bản
        ItemStack item = createItemStackFromMaterial(materialConfig);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(Chat.colorize(name
                .replace("{material}", materialName)
                .replace("{receiver}", receiver.getName())));

        // Xử lý lore
        if (lore != null && !lore.isEmpty()) {
            meta.setLore(lore.stream()
                    .map(line -> Chat.colorize(line
                            .replace("{material}", materialName)
                            .replace("{receiver}", receiver.getName())))
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Áp dụng custom model data cho custom buttons
        applyCustomModelData(meta, basePath + ".custom-model-data");

        item.setItemMeta(meta);

        // Tạo InteractiveItem với click handler
        InteractiveItem interactiveItem = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            player.closeInventory();
            player.sendMessage(Chat.colorize(
                Objects.requireNonNull(File.getMessage().getString("user.action.transfer.chat_number", "&8[&b&l❖&8] &aNhập số lượng muốn chuyển:"))));

            // Thêm người chơi vào danh sách chat
            SimpleEntry<Player, String> transferInfo = new SimpleEntry<>(receiver, material);
            com.hongminh54.storage.Listeners.Chat.chat_transfer.put(player, transferInfo);
        });

        inventory.setItem(interactiveItem.getSlot(), interactiveItem);
    }

    /**
     * Thêm nút chuyển tất cả từ config
     * @param materialName Tên vật liệu
     * @param currentAmount Số lượng hiện có
     */
    private void addAllQuantityButton(String materialName, int currentAmount) {
        String basePath = "transfer_gui.quantity_buttons.all";
        int slot = config.getInt(basePath + ".slot", -1);
        if (slot == -1) return;

        String materialConfig = config.getString(basePath + ".material", "CHEST");
        String name = config.getString(basePath + ".name", "&aChuyển tất cả");
        List<String> lore = config.getStringList(basePath + ".lore");

        // Tạo item với xử lý tương thích đa phiên bản
        ItemStack item = createItemStackFromMaterial(materialConfig);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(Chat.colorize(name
                .replace("{material}", materialName)
                .replace("{receiver}", receiver.getName())
                .replace("{amount}", String.valueOf(currentAmount))));

        // Xử lý lore
        if (lore != null && !lore.isEmpty()) {
            meta.setLore(lore.stream()
                    .map(line -> Chat.colorize(line
                            .replace("{material}", materialName)
                            .replace("{receiver}", receiver.getName())
                            .replace("{amount}", String.valueOf(currentAmount))))
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Áp dụng custom model data cho all quantity buttons
        applyCustomModelData(meta, basePath + ".custom-model-data");

        item.setItemMeta(meta);

        // Tạo InteractiveItem với click handler
        InteractiveItem interactiveItem = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            // Kiểm tra số lượng hiện có (lấy lại giá trị mới nhất)
            int amount = MineManager.getPlayerBlock(player, material);
            if (amount <= 0) {
                player.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                        .replace("#material#", materialName)
                        .replace("#amount#", "0")));
                try {
                    String failSound = config.getString("transfer_gui.fail_sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    SoundManager.playSoundFromConfig(player, failSound);
                } catch (Exception e) {
                    // Bỏ qua lỗi âm thanh
                }
                return;
            }

            // Đọc phần trăm chuyển tài nguyên từ cấu hình
            int transferPercentage = File.getConfig().getInt("settings.transfer_percentage", 25);

            // Chỉ chuyển transferPercentage% số lượng khi nhấp nút "Tất cả" để tránh lỡ tay
            int transferAmount = Math.max(1, (amount * transferPercentage) / 100);

            // Thông báo lý do chỉ chuyển transferPercentage%
            if (amount >= 4) {
                String transferLimitMessage = Objects.requireNonNull(File.getMessage().getString("user.action.transfer.transfer_limit",
                    "&8[&e&l!&8] &eGiới hạn chuyển: &a" + transferPercentage + "% &etài nguyên mỗi lần để tránh lỡ tay"));
                player.sendMessage(Chat.colorize(transferLimitMessage.replace("#percentage#", String.valueOf(transferPercentage))));
            }

            // Xử lý chuyển tài nguyên
            transferResource(player, receiver, material, transferAmount);
        });

        inventory.setItem(interactiveItem.getSlot(), interactiveItem);
    }

    /**
     * Thêm nút điều khiển từ config
     * @param materialName Tên vật liệu
     */
    private void addControlButtons(String materialName) {
        // Nút hủy
        String basePath = "transfer_gui.control_buttons.cancel";
        int slot = config.getInt(basePath + ".slot", 40);
        String materialConfig = config.getString(basePath + ".material", "BARRIER");
        String name = config.getString(basePath + ".name", "&c&lHủy");
        List<String> lore = config.getStringList(basePath + ".lore");

        // Tạo item với xử lý tương thích đa phiên bản
        ItemStack item = createItemStackFromMaterial(materialConfig);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(Chat.colorize(name
                .replace("{material}", materialName)
                .replace("{receiver}", receiver.getName())));

        // Xử lý lore
        if (lore != null && !lore.isEmpty()) {
            meta.setLore(lore.stream()
                    .map(line -> Chat.colorize(line
                            .replace("{material}", materialName)
                            .replace("{receiver}", receiver.getName())))
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Áp dụng custom model data cho control buttons
        applyCustomModelData(meta, basePath + ".custom-model-data");

        item.setItemMeta(meta);

        // Tạo InteractiveItem với click handler
        InteractiveItem interactiveItem = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            player.closeInventory();
            // Sử dụng Bukkit scheduler để mở PersonalStorage
            Bukkit.getScheduler().runTask(Storage.getStorage(), () ->
                player.openInventory(new PersonalStorage(player).getInventory())
            );
        });

        inventory.setItem(interactiveItem.getSlot(), interactiveItem);
    }

    /**
     * Áp dụng custom model data cho ItemMeta nếu phiên bản hỗ trợ
     * @param meta ItemMeta cần áp dụng
     * @param configPath Đường dẫn config cho custom model data
     */
    private void applyCustomModelData(ItemMeta meta, String configPath) {
        if (meta == null) return;

        try {
            // Kiểm tra phiên bản có hỗ trợ Custom Model Data không (1.14+)
            if (MaterialCompatibility.isPre113() || nmsAssistant.isVersionLessThan(14)) {
                return; // Không hỗ trợ trong phiên bản cũ
            }

            // Đọc custom model data từ config
            if (config.contains(configPath)) {
                int customModelData = config.getInt(configPath, -1);
                if (customModelData > 0) {
                    try {
                        // Sử dụng reflection để gọi setCustomModelData an toàn
                        java.lang.reflect.Method setCustomModelDataMethod = meta.getClass().getMethod("setCustomModelData", Integer.class);
                        setCustomModelDataMethod.invoke(meta, customModelData);

                        if (Storage.getStorage().isDebug()) {
                            Storage.getStorage().getLogger().info("Đã áp dụng CustomModelData " + customModelData + " cho " + configPath);
                        }
                    } catch (NoSuchMethodException | SecurityException | IllegalAccessException | java.lang.reflect.InvocationTargetException e) {
                        // Fallback: sử dụng method trực tiếp nếu reflection thất bại
                        try {
                            meta.setCustomModelData(customModelData);
                        } catch (NoSuchMethodError fallbackError) {
                            // Phiên bản không hỗ trợ CustomModelData
                            if (Storage.getStorage().isDebug()) {
                                Storage.getStorage().getLogger().warning("CustomModelData không được hỗ trợ trong phiên bản này");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            handleError("Failed to apply custom model data: " + e.getMessage(), false);
        }
    }

    /**
     * Thêm nút xác nhận và hủy (method cũ - deprecated)
     * @param materialName Tên tài nguyên
     */
    @Deprecated
    private void addControlButtonsOld(String materialName) {
        // Đọc phần trăm chuyển tài nguyên từ cấu hình
        final int transferPercentage = File.getConfig().getInt("settings.transfer_percentage", 25);
        final int playerAmount = MineManager.getPlayerBlock(sender, material);
        final int defaultTransferAmount = (playerAmount < 4) ? playerAmount : Math.max(1, (playerAmount * transferPercentage) / 100);
        
        // Thêm nút xác nhận
        ItemStack confirmItem = ItemManager.createItem(
                Material.EMERALD_BLOCK, 
                "&a&lXác nhận chuyển", 
                Arrays.asList(
                    "&7Chuyển &f" + defaultTransferAmount + " &b" + materialName + " &7cho &a" + receiver.getName(),
                    "&7",
                    "&eNhấp để xác nhận"
                )
        );
        
        InteractiveItem confirmButton = new InteractiveItem(confirmItem, 40).onClick((player, clickType) -> {
            player.closeInventory();
            
            // Mở một giao diện xác nhận
            int currentAmount = MineManager.getPlayerBlock(player, material);
            if (currentAmount <= 0) {
                player.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                        .replace("#material#", materialName)
                        .replace("#amount#", "0")));
                try {
                    // Sử dụng âm thanh thất bại từ cấu hình với SoundCompatibility
                    String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    com.hongminh54.storage.Utils.SoundCompatibility.playSoundFromConfig(player, failSoundConfig);
                } catch (Exception e) {
                    // Phát âm thanh cảnh báo
                    com.hongminh54.storage.Utils.SoundCompatibility.playSound(player, "NOTE_BASS", 0.5f, 0.8f);
                }
                return;
            }
            
            // Người chơi không còn trực tuyến
            if (!receiver.isOnline()) {
                player.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.player_not_online"))
                        .replace("#player#", receiver.getName())));
                try {
                    // Sử dụng âm thanh thất bại từ cấu hình
                    String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    String[] soundParts = failSoundConfig.split(":");
                    org.bukkit.Sound sound = org.bukkit.Sound.valueOf(soundParts[0]);
                    float volume = soundParts.length > 1 ? Float.parseFloat(soundParts[1]) : 1.0f;
                    float pitch = soundParts.length > 2 ? Float.parseFloat(soundParts[2]) : 1.0f;
                    
                    player.playSound(player.getLocation(), sound, volume, pitch);
                } catch (Exception e) {
                    // Phát âm thanh cảnh báo
                    try {
                        player.playSound(player.getLocation(), org.bukkit.Sound.valueOf("NOTE_BASS"), 0.5f, 0.8f);
                    } catch (Exception ex) {
                        // Bỏ qua nếu không hỗ trợ
                    }
                }
                return;
            }
            
            // Chuyển tối đa transferPercentage% tài nguyên cho mỗi lần xác nhận
            int transferAmount = (currentAmount < 4) ? currentAmount : Math.max(1, (currentAmount * transferPercentage) / 100);
            int percentage = (transferAmount * 100) / Math.max(1, currentAmount);
            
            // Giải thích lý do chỉ cho chuyển transferPercentage% mỗi lần
            String transferLimitMessage = Objects.requireNonNull(File.getMessage().getString("user.action.transfer.transfer_limit", 
                "&8[&e&l!&8] &eGiới hạn chuyển: &a" + transferPercentage + "% &etài nguyên mỗi lần để tránh lỡ tay"));
            
            if (currentAmount < 4) {
                player.sendMessage(Chat.colorize("&8[&e&l❖&8] &aĐang chuyển &f" + transferAmount + " &a" + materialName + 
                    " &fcho &e" + receiver.getName() + " &7(Số lượng ít nên chuyển hết)"));
            } else {
                player.sendMessage(Chat.colorize("&8[&e&l❖&8] &aĐang chuyển &f" + transferAmount + " &a" + materialName + 
                        " &7(" + percentage + "% tổng số) &fcho &e" + receiver.getName()));
                player.sendMessage(Chat.colorize(transferLimitMessage));
            }
                    
            transferResource(player, receiver, material, transferAmount);
        });
        
        inventory.setItem(confirmButton.getSlot(), confirmButton);
        
        // Thêm nút hủy
        ItemStack cancelItem = ItemManager.createItem(
                Material.BARRIER, 
                "&c&lHủy", 
                Arrays.asList(
                    "&7Quay lại kho cá nhân",
                    "&7",
                    "&eNhấp để hủy"
                )
        );
        
        InteractiveItem cancelButton = new InteractiveItem(cancelItem, 44).onClick((player, clickType) -> {
            player.closeInventory();
            new BukkitRunnable() {
                @Override
                public void run() {
                    player.openInventory(new PersonalStorage(player).getInventory());
                }
            }.runTask(Storage.getStorage());
        });
        
        inventory.setItem(cancelButton.getSlot(), cancelButton);
    }
    
    /**
     * Thêm nút số lượng cố định vào giao diện
     * @param inventory Giao diện
     * @param slot Vị trí
     * @param amount Số lượng
     * @param materialName Tên tài nguyên
     */
    private void addQuantityButton(Inventory inventory, int slot, int amount, String materialName) {
        ItemStack item = ItemManager.createItem(
                Material.PAPER, 
                "&a&l" + amount, 
                Arrays.asList(
                    "&7Chuyển &a" + amount + " " + materialName,
                    "&7cho &a" + receiver.getName(),
                    "&7",
                    "&eNhấp để chọn"
                )
        );
        
        InteractiveItem button = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            int currentAmount = MineManager.getPlayerBlock(player, material);
            if (currentAmount < amount) {
                player.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                        .replace("#material#", materialName)
                        .replace("#amount#", String.valueOf(currentAmount))));
                return;
            }
            
            // Xử lý chuyển tài nguyên
            transferResource(player, receiver, material, amount);
        });
        
        inventory.setItem(button.getSlot(), button);
    }
    
    /**
     * Thêm nút số lượng tùy chỉnh vào giao diện
     * @param inventory Giao diện
     * @param slot Vị trí
     * @param materialName Tên tài nguyên
     */
    private void addCustomQuantityButton(Inventory inventory, int slot, String materialName) {
        ItemStack item = ItemManager.createItem(
                Material.NAME_TAG, 
                "&a&lTùy chỉnh", 
                Arrays.asList(
                    "&7Nhập số lượng tùy chỉnh",
                    "&7để chuyển cho &a" + receiver.getName(),
                    "&7",
                    "&eNhấp để chọn"
                )
        );
        
        InteractiveItem button = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            player.closeInventory();
            player.sendMessage(Chat.colorize(
                Objects.requireNonNull(File.getMessage().getString("user.action.transfer.chat_number", "&8[&b&l❖&8] &aNhập số lượng muốn chuyển:"))));
            
            // Thêm người chơi vào danh sách chat
            SimpleEntry<Player, String> transferInfo = new SimpleEntry<>(receiver, material);
            com.hongminh54.storage.Listeners.Chat.chat_transfer.put(player, transferInfo);
        });
        
        inventory.setItem(button.getSlot(), button);
    }
    
    /**
     * Thêm nút chuyển tất cả vào giao diện
     * @param inventory Giao diện
     * @param slot Vị trí
     * @param currentAmount Số lượng hiện có
     * @param materialName Tên tài nguyên
     */
    private void addAllQuantityButton(Inventory inventory, int slot, int currentAmount, String materialName) {
        // Đọc phần trăm chuyển tài nguyên từ cấu hình
        int transferPercentage = File.getConfig().getInt("settings.transfer_percentage", 25);
        
        ItemStack item = ItemManager.createItem(
                Material.HOPPER, 
                "&a&lTất cả (" + currentAmount + ")", 
                Arrays.asList(
                    "&7Chuyển tất cả &a" + currentAmount + " " + materialName,
                    "&7cho &a" + receiver.getName(),
                    "&7",
                    "&cLưu ý: &7Sẽ chỉ chuyển &a" + transferPercentage + "% &7mỗi lần nhấp",
                    "&7để tránh việc lỡ tay chuyển tất cả",
                    "&7",
                    "&eNhấp để chọn"
                )
        );
        
        InteractiveItem button = new InteractiveItem(item, slot).onClick((player, clickType) -> {
            // Kiểm tra số lượng hiện có (lấy lại giá trị mới nhất)
            int amount = MineManager.getPlayerBlock(player, material);
            if (amount <= 0) {
                player.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                        .replace("#material#", materialName)
                        .replace("#amount#", "0")));
                try {
                    // Sử dụng âm thanh thất bại từ cấu hình
                    String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    if (failSoundConfig != null && !failSoundConfig.isEmpty()) {
                        com.hongminh54.storage.Utils.SoundCompatibility.playSoundFromConfig(player, failSoundConfig);
                    }
                } catch (Exception e) {
                    // Bỏ qua nếu âm thanh không hỗ trợ
                }
                return;
            }
            
            // Chỉ chuyển transferPercentage% số lượng khi nhấp nút "Tất cả" để tránh lỡ tay
            int transferAmount = Math.max(1, (amount * transferPercentage) / 100);
            
            // Thông báo lý do chỉ chuyển transferPercentage%
            if (amount >= 4) {
                String transferLimitMessage = Objects.requireNonNull(File.getMessage().getString("user.action.transfer.transfer_limit", 
                    "&8[&e&l!&8] &eGiới hạn chuyển: &a" + transferPercentage + "% &etài nguyên mỗi lần để tránh lỡ tay"));
                player.sendMessage(Chat.colorize(transferLimitMessage.replace("#percentage#", String.valueOf(transferPercentage))));
            }
            
            // Xử lý chuyển tài nguyên
            transferResource(player, receiver, material, transferAmount);
        });
        
        inventory.setItem(button.getSlot(), button);
    }
    
    /**
     * Xử lý chuyển tài nguyên giữa người chơi
     * @param sender Người gửi
     * @param receiver Người nhận
     * @param material Loại tài nguyên
     * @param amount Số lượng
     */
    public void transferResource(Player sender, Player receiver, String material, int amount) {
        if (sender == null || !sender.isOnline() || receiver == null || !receiver.isOnline()) {
            if (sender != null && sender.isOnline()) {
                sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.player_not_online"))
                        .replace("#player#", receiver != null ? receiver.getName() : "người chơi")));
            }
            return;
        }
        
        // Kiểm tra xem người chơi có quyền chuyển tài nguyên không
        if (!com.hongminh54.storage.Utils.TransferManager.canTransfer(sender)) {
            sender.sendMessage(Chat.colorize("&8[&c&l⏱&8] &cBạn cần đợi một chút trước khi thực hiện giao dịch tiếp theo."));
            
            // Phát âm thanh thất bại
            try {
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                String[] soundParts = failSoundConfig.split(":");
                org.bukkit.Sound sound = org.bukkit.Sound.valueOf(soundParts[0]);
                float volume = soundParts.length > 1 ? Float.parseFloat(soundParts[1]) : 1.0f;
                float pitch = soundParts.length > 2 ? Float.parseFloat(soundParts[2]) : 1.0f;
                
                sender.playSound(sender.getLocation(), sound, volume, pitch);
            } catch (Exception e) {
                // Bỏ qua nếu không hỗ trợ
            }
            return;
        }
        
        // Kiểm tra xem người chơi đã có giao dịch đang xử lý không
        if (com.hongminh54.storage.Utils.TransferDelayManager.isPlayerTransferring(sender)) {
            sender.sendMessage(Chat.colorize("&8[&c&l⏱&8] &cBạn đang có giao dịch đang xử lý. Vui lòng đợi hoàn tất."));
            return;
        }
        
        // Kiểm tra xem người chơi có đủ tài nguyên không
        int senderAmount = MineManager.getPlayerBlock(sender, material);
        final String materialName = File.getConfig().getString("items." + material, material != null ? material.split(";")[0] : "unknown");
        
        if (senderAmount < amount) {
            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                    .replace("#material#", materialName)
                    .replace("#amount#", String.valueOf(senderAmount))));
            
            // Phát âm thanh thất bại
            try {
                // Sử dụng âm thanh thất bại từ cấu hình
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                com.hongminh54.storage.Utils.SoundCompatibility.playSoundFromConfig(sender, failSoundConfig);
            } catch (Exception e) {
                // Phát âm thanh cảnh báo
                com.hongminh54.storage.Utils.SoundCompatibility.playSound(sender, "NOTE_BASS", 0.5f, 0.8f);
            }
            return;
        }
        
        // Kiểm tra giới hạn kho của người nhận
        int receiverAmount = MineManager.getPlayerBlock(receiver, material);
        final int maxStorage = MineManager.getMaxBlock(receiver);
        
        // Kiểm tra xem kho của người nhận có đủ chỗ không
        if (receiverAmount >= maxStorage) {
            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.receiver_full"))
                    .replace("#player#", receiver.getName())));
            
            // Phát âm thanh thất bại
            try {
                // Sử dụng âm thanh thất bại từ cấu hình
                String failSoundConfig = File.getConfig().getString("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                com.hongminh54.storage.Utils.SoundCompatibility.playSoundFromConfig(sender, failSoundConfig);
            } catch (Exception e) {
                // Phát âm thanh cảnh báo
                com.hongminh54.storage.Utils.SoundCompatibility.playSound(sender, "NOTE_BASS", 0.5f, 0.8f);
            }
            return;
        }
        
        // Kiểm tra không gian còn lại của người nhận
        int availableSpace = maxStorage - receiverAmount;
        int transferAmount = amount;
        
        // Nếu không đủ không gian, chỉ chuyển số lượng có thể
        if (amount > availableSpace) {
            transferAmount = availableSpace;
            
            // Thông báo cho người gửi về việc chỉ chuyển được một phần
            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.partial_success",
                    "&8[&e&l!&8] &eChỉ có thể chuyển &f" + transferAmount + " &e#material# &7(giới hạn kho của người nhận)"))
                    .replace("#amount#", String.valueOf(transferAmount))
                    .replace("#material#", materialName)));
        }
        
        // Đóng GUI để tránh click nhiều lần
        sender.closeInventory();
        
        // Lưu giá trị cuối cùng để sử dụng trong callback
        final int finalTransferAmount = transferAmount;
        
        // Đăng ký vào TransferDelayManager với callback
        com.hongminh54.storage.Utils.TransferDelayManager.registerTransfer(
            sender, receiver, material, finalTransferAmount, 
            new com.hongminh54.storage.Utils.TransferDelayManager.TransferCallback() {
                @Override
                public void onTransferReady(Player sender, Player receiver, String material, int amount) {
                    // Sử dụng khóa đồng bộ hóa để ngăn việc chuyển tài nguyên đồng thời
                    String transferLockKey = "transfer_lock_" + sender.getUniqueId() + "_" + receiver.getUniqueId();
                    synchronized (transferLockKey.intern()) {
                        // Kiểm tra lại số lượng hiện có (có thể đã thay đổi trong thời gian chờ)
                        int currentAmount = MineManager.getPlayerBlock(sender, material);
                        if (currentAmount < amount) {
                            // Thông báo không đủ tài nguyên
                            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.not_enough"))
                                    .replace("#material#", materialName)
                                    .replace("#amount#", String.valueOf(currentAmount))));
                            
                            com.hongminh54.storage.Utils.TransferManager.playTransferFailEffect(sender);
            return;
        }
        
                        // Kiểm tra lại không gian của người nhận
                        int currentReceiverAmount = MineManager.getPlayerBlock(receiver, material);
                        int currentAvailableSpace = maxStorage - currentReceiverAmount;
                        
                        if (currentAvailableSpace <= 0) {
                            sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.receiver_full"))
                                    .replace("#player#", receiver.getName())));
                            
                            com.hongminh54.storage.Utils.TransferManager.playTransferFailEffect(sender);
            return;
        }
        
                        // Điều chỉnh số lượng nếu cần
                        int actualTransferAmount = Math.min(amount, currentAvailableSpace);
                        
                        // Sử dụng phương thức transferResourcesWithConsistencyCheck để đảm bảo tính nhất quán dữ liệu
                        Map<String, Integer> resources = new HashMap<>();
                        resources.put(material, actualTransferAmount);
                        
                        Map<String, Integer> transferredResources = com.hongminh54.storage.Utils.TransferManager.transferResourcesWithConsistencyCheck(sender, receiver, resources);
                        
                        // Kiểm tra kết quả chuyển kho
                        if (transferredResources.containsKey(material) && transferredResources.get(material) > 0) {
                            int actualTransferred = transferredResources.get(material);
        
        // Thông báo thành công cho người gửi
        sender.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.transfer_item"))
                                    .replace("#amount#", String.valueOf(actualTransferred))
                .replace("#material#", materialName)
                .replace("#player#", receiver.getName())));
        
        // Thông báo cho người nhận
        receiver.sendMessage(Chat.colorize(Objects.requireNonNull(File.getMessage().getString("user.action.transfer.receive_item"))
                                    .replace("#amount#", String.valueOf(actualTransferred))
                .replace("#material#", materialName)
                .replace("#player#", sender.getName())));
        
        // Phát hiệu ứng và âm thanh
        applyTransferEffects(sender, receiver);
                            
                            // Gọi phương thức playTransferEffects để hiển thị hiệu ứng chuyển giao thành công
                            com.hongminh54.storage.Utils.TransferManager.playTransferEffects(sender, receiver, actualTransferred);
                        } else {
                            // Thông báo thất bại
                            sender.sendMessage(Chat.colorize("&8[&c&l✕&8] &cĐã xảy ra lỗi khi chuyển tài nguyên. Vui lòng thử lại sau."));
                            
                            // Gọi phương thức playTransferFailEffect để hiển thị hiệu ứng thất bại
                            com.hongminh54.storage.Utils.TransferManager.playTransferFailEffect(sender);
                        }
                    }
                }
            }
        );
    }
    
    /**
     * Áp dụng hiệu ứng sau khi chuyển tài nguyên
     * 
     * @param sender Người gửi
     * @param receiver Người nhận
     */
    private void applyTransferEffects(Player sender, Player receiver) {
        // Đọc tham số hiệu ứng từ cấu hình
        int maxParticleCount = File.getConfig().getInt("settings.max_particle_count", 15);
        
        // Hiệu ứng bên người gửi
        try {
            // Hiệu ứng âm thanh
            String senderSoundConfig = File.getConfig().getString("effects.transfer_success.sender_sound", "ENTITY_PLAYER_LEVELUP:0.5:1.2");
            if (senderSoundConfig != null && !senderSoundConfig.isEmpty()) {
                String[] parts = senderSoundConfig.split(":");
                org.bukkit.Sound sound = org.bukkit.Sound.valueOf(parts[0]);
                float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 0.5f;
                float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.2f;
                sender.playSound(sender.getLocation(), sound, volume, pitch);
            }
            
            // Hiệu ứng hạt - giảm số lượng
            String senderParticleConfig = File.getConfig().getString("effects.transfer_success.sender_particle", "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:10");
            if (senderParticleConfig != null && !senderParticleConfig.isEmpty() && maxParticleCount > 0) {
                String[] parts = senderParticleConfig.split(":");
                org.bukkit.Particle particle = org.bukkit.Particle.valueOf(parts[0]);
                double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.5;
                double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.5;
                double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.5;
                double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.1;
                int count = parts.length > 5 ? Integer.parseInt(parts[5]) : 10;
                
                // Giới hạn số lượng hạt chặt chẽ hơn
                count = Math.min(count, maxParticleCount);
                count = Math.min(count, 15); // Hard limit ở 15 hạt
                
                sender.getWorld().spawnParticle(particle, sender.getLocation().add(0, 1, 0), 
                        count, offsetX, offsetY, offsetZ, speed);
            }
        } catch (Exception e) {
            // Bỏ qua nếu hiệu ứng không hỗ trợ
        }
        
        // Hiệu ứng bên người nhận
        if (receiver == null || !receiver.isOnline()) return;
        
        try {
            // Hiệu ứng âm thanh
            String receiverSoundConfig = File.getConfig().getString("effects.transfer_success.receiver_sound", "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0");
            if (receiverSoundConfig != null && !receiverSoundConfig.isEmpty()) {
                com.hongminh54.storage.Utils.SoundCompatibility.playSoundFromConfig(receiver, receiverSoundConfig);
            }
            
            // Hiệu ứng hạt - giảm số lượng
            String receiverParticleConfig = File.getConfig().getString("effects.transfer_success.receiver_particle", "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:10");
            if (receiverParticleConfig != null && !receiverParticleConfig.isEmpty() && maxParticleCount > 0) {
                String[] parts = receiverParticleConfig.split(":");
                org.bukkit.Particle particle = org.bukkit.Particle.valueOf(parts[0]);
                double offsetX = parts.length > 1 ? Double.parseDouble(parts[1]) : 0.5;
                double offsetY = parts.length > 2 ? Double.parseDouble(parts[2]) : 0.5;
                double offsetZ = parts.length > 3 ? Double.parseDouble(parts[3]) : 0.5;
                double speed = parts.length > 4 ? Double.parseDouble(parts[4]) : 0.1;
                int count = parts.length > 5 ? Integer.parseInt(parts[5]) : 10;
                
                // Giới hạn số lượng hạt chặt chẽ hơn
                count = Math.min(count, maxParticleCount);
                count = Math.min(count, 15); // Hard limit ở 15 hạt
                
                receiver.getWorld().spawnParticle(particle, receiver.getLocation().add(0, 1, 0), 
                        count, offsetX, offsetY, offsetZ, speed);
            }
        } catch (Exception e) {
            // Bỏ qua nếu hiệu ứng không hỗ trợ
        }
    }
    
    /**
     * Lấy người chơi gửi
     * @return Người gửi
     */
    public Player getSender() {
        return sender;
    }
    
    /**
     * Lấy người chơi nhận
     * @return Người nhận
     */
    public Player getReceiver() {
        return receiver;
    }
    
    /**
     * Lấy loại tài nguyên
     * @return Loại tài nguyên
     */
    public String getMaterial() {
        return material;
    }
    
    /**
     * Lấy cấu hình
     * @return Cấu hình
     */
    public FileConfiguration getConfig() {
        return config;
    }

    /**
     * Xử lý sự kiện click vào inventory
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        // Kiểm tra nếu GUI này là một phần của view của người chơi
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory != inventory) {
            return;
        }
        
        // Hủy tất cả các sự kiện click để ngăn người chơi lấy item 
        // Quan trọng: Ngăn chặn người chơi click vào bất kỳ slot nào trong view
        event.setCancelled(true);
        
        // Cập nhật inventory ngay lập tức để đảm bảo thay đổi được áp dụng
        if (event.getWhoClicked() instanceof Player) {
            ((Player) event.getWhoClicked()).updateInventory();
        }
        
        // Xử lý các click vào inventory này nếu ItemStack không null và thuộc inventory trên cùng
        // Chỉ xử lý click vào inventory của GUI
        if (event.getCurrentItem() == null || event.getClickedInventory() == null || event.getClickedInventory() != inventory) {
            return;
        }
        
        // Các nút tương tác đã được tạo với InteractiveItem và xử lý riêng thông qua class GUI
    }
    
    /**
     * Xử lý sự kiện đóng inventory
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().equals(inventory)) {
            // Hủy đăng ký listener khi đóng giao diện
            new BukkitRunnable() {
                @Override
                public void run() {
                    unregisterListener();
                }
            }.runTaskLater(Storage.getStorage(), 1L);
        }
    }
    
    /**
     * Xử lý sự kiện kéo vật phẩm trong inventory
     * Ngăn người chơi kéo vật phẩm trong GUI
     */
    @EventHandler
    public void onInventoryDrag(InventoryDragEvent event) {
        // Sử dụng InventoryCompatibility để lấy top inventory một cách an toàn
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory != null && topInventory.equals(inventory)) {
            // Hủy tất cả các sự kiện kéo vật phẩm liên quan đến GUI này
            event.setCancelled(true);

            // Cập nhật inventory ngay lập tức để đảm bảo UI ổn định
            if (event.getWhoClicked() instanceof Player) {
                ((Player) event.getWhoClicked()).updateInventory();
            }
        }
    }

    /**
     * Tạo ItemStack từ material string với xử lý tương thích đa phiên bản
     * @param materialString Material string (có thể có format "MATERIAL;DATA")
     * @return ItemStack tương ứng
     */
    private ItemStack createItemStackFromMaterial(String materialString) {
        // Sử dụng function mới từ MaterialCompatibility
        return MaterialCompatibility.createCompatibleItemStack(materialString);
    }


}